<?php
/**
 * Quick Database Test
 * اختبار سريع لقاعدة البيانات
 */

require_once 'php/config.php';

echo "=== Quick Database Test ===\n";
echo "اختبار سريع لقاعدة البيانات\n\n";

try {
    $pdo = getPDOConnection();
    echo "✅ Database connection: OK\n";
    echo "✅ الاتصال بقاعدة البيانات: نجح\n\n";
    
    // Test produits table
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM produits');
    $count = $stmt->fetch()['count'];
    echo "📊 Products count: $count\n";
    echo "📊 عدد المنتجات: $count\n\n";
    
    // Test user_roles table
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM user_roles');
    $rolesCount = $stmt->fetch()['count'];
    echo "🛡️ Roles count: $rolesCount\n";
    echo "🛡️ عدد الأدوار: $rolesCount\n\n";
    
    // Test users table
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM users');
    $usersCount = $stmt->fetch()['count'];
    echo "👥 Users count: $usersCount\n";
    echo "👥 عدد المستخدمين: $usersCount\n\n";
    
    // Test foreign keys
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME = 'produits'
    ");
    $fkCount = $stmt->fetch()['count'];
    echo "🔗 Foreign keys to produits: $fkCount\n";
    echo "🔗 المفاتيح الخارجية لـ produits: $fkCount\n\n";
    
    echo "🎉 System status: OPERATIONAL\n";
    echo "🎉 حالة النظام: يعمل بشكل طبيعي\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "=== اكتمل الاختبار ===\n";
?>
