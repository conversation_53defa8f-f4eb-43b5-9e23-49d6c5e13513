<?php

/**
 * Security Settings API
 * Handles security configuration and monitoring
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config.php';

// Get PDO connection using the existing config
try {
    $pdo = getPDOConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get';

    switch ($action) {
        case 'get':
        case 'get_settings':
            getSecuritySettings();
            break;

        case 'update':
            updateSecuritySettings();
            break;

        case 'save_authentication':
            saveAuthenticationSettings();
            break;

        case 'logs':
            getSecurityLogs();
            break;

        case 'test':
            testSecurity();
            break;

        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * Get security settings
 */
function getSecuritySettings()
{
    global $pdo;

    try {
        // Create security settings table if it doesn't exist
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(255) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // Create security logs table if it doesn't exist
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            event_type VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // Insert default security settings
        $defaultSettings = [
            ['session_timeout', '3600', 'مهلة انتهاء الجلسة (بالثواني)'],
            ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
            ['password_min_length', '8', 'الحد الأدنى لطول كلمة المرور'],
            ['require_strong_password', '1', 'طلب كلمة مرور قوية'],
            ['enable_two_factor', '0', 'تفعيل المصادقة الثنائية'],
            ['auto_logout', '1', 'تسجيل الخروج التلقائي'],
            ['ip_whitelist', '', 'قائمة IP المسموحة'],
            ['enable_ssl', '1', 'إجبار استخدام SSL']
        ];

        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO security_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }

        // Get all settings
        $stmt = $pdo->query("SELECT * FROM security_settings ORDER BY setting_key");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to key-value format
        $settingsData = [];
        foreach ($settings as $setting) {
            $settingsData[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'description' => $setting['description'],
                'updated_at' => $setting['updated_at']
            ];
        }

        // Get security statistics
        $stats = getSecurityStats();

        echo json_encode([
            'success' => true,
            'data' => [
                'settings' => $settingsData,
                'stats' => $stats
            ],
            'message' => 'تم تحميل إعدادات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل إعدادات الأمان: ' . $e->getMessage());
    }
}

/**
 * Update security settings
 */
function updateSecuritySettings()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['settings'])) {
            throw new Exception('بيانات غير صحيحة');
        }

        $pdo->beginTransaction();

        foreach ($input['settings'] as $key => $value) {
            $stmt = $pdo->prepare("UPDATE security_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        }

        // Log security settings change
        logSecurityEvent('settings_updated', 'تم تحديث إعدادات الأمان');

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث إعدادات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('فشل في تحديث إعدادات الأمان: ' . $e->getMessage());
    }
}

/**
 * Get security logs
 */
function getSecurityLogs()
{
    global $pdo;

    try {
        $limit = $_GET['limit'] ?? 50;
        $stmt = $pdo->prepare("SELECT * FROM security_logs ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $logs,
            'message' => 'تم تحميل سجلات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل سجلات الأمان: ' . $e->getMessage());
    }
}

/**
 * Test security configuration
 */
function testSecurity()
{
    global $pdo;

    try {
        $tests = [
            'database_connection' => testDatabaseConnection(),
            'ssl_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'session_security' => session_status() === PHP_SESSION_ACTIVE,
            'file_permissions' => is_writable('../uploads/'),
        ];

        echo json_encode([
            'success' => true,
            'data' => $tests,
            'message' => 'تم اختبار الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في اختبار الأمان: ' . $e->getMessage());
    }
}

/**
 * Get security statistics
 */
function getSecurityStats()
{
    global $pdo;

    try {
        $stats = [];

        // Count recent login attempts
        $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE event_type = 'login_attempt' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['login_attempts_24h'] = $stmt->fetchColumn();

        // Count failed logins
        $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE event_type = 'login_failed' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['failed_logins_24h'] = $stmt->fetchColumn();

        // Security level calculation
        $securityLevel = calculateSecurityLevel();
        $stats['security_level'] = $securityLevel;

        return $stats;
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

/**
 * Log security event
 */
function logSecurityEvent($eventType, $description)
{
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO security_logs (event_type, description, ip_address, user_agent) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $eventType,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // Silent fail for logging
    }
}

/**
 * Test database connection
 */
function testDatabaseConnection()
{
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Calculate security level
 */
function calculateSecurityLevel()
{
    // Simple security level calculation
    $level = 'متوسط';

    // Check various security factors
    $factors = [
        isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        session_status() === PHP_SESSION_ACTIVE,
        is_writable('../uploads/') === false, // Should not be writable for security
    ];

    $score = array_sum($factors);

    if ($score >= 2) {
        $level = 'عالي';
    } elseif ($score >= 1) {
        $level = 'متوسط';
    } else {
        $level = 'منخفض';
    }

    return $level;
}
