<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إعدادات الدفع</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-family: inherit;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .quick-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .payment-demo {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }
        
        .payment-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }
        
        .payment-icon.credit_card { background: linear-gradient(135deg, #3498db, #2980b9); }
        .payment-icon.bank_transfer { background: linear-gradient(135deg, #27ae60, #229954); }
        .payment-icon.cash_on_delivery { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .payment-icon.digital_wallet { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .payment-icon.cryptocurrency { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        
        .payment-info h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }
        
        .payment-info p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .payment-fees {
            margin-right: auto;
            text-align: left;
            font-size: 0.85rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-credit-card"></i>
                اختبار نظام إعدادات الدفع
            </h1>
            <p>فحص شامل لجميع مكونات نظام إدارة طرق الدفع</p>
        </div>

        <div class="test-grid">
            <!-- API Test -->
            <div class="test-section">
                <h3><i class="fas fa-plug"></i> اختبار API طرق الدفع</h3>
                <p>فحص جميع نقاط API لإدارة طرق الدفع</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="testPaymentAPI()">
                        <i class="fas fa-play"></i>
                        اختبار API
                    </button>
                </div>

                <div class="loading" id="apiLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص API...</p>
                </div>

                <div class="results" id="apiResults" style="display: none;">
                    <h4>نتائج فحص API</h4>
                    <div id="apiResultsList"></div>
                </div>
            </div>

            <!-- Payment Methods Demo -->
            <div class="test-section">
                <h3><i class="fas fa-money-check-alt"></i> عرض طرق الدفع</h3>
                <p>عرض طرق الدفع المتاحة مع التفاصيل</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-success" onclick="loadPaymentMethods()">
                        <i class="fas fa-list"></i>
                        تحميل طرق الدفع
                    </button>
                </div>

                <div class="loading" id="methodsLoading">
                    <div class="spinner"></div>
                    <p>جاري تحميل طرق الدفع...</p>
                </div>

                <div class="payment-demo" id="paymentDemo" style="display: none;">
                    <h4>طرق الدفع المتاحة</h4>
                    <div id="paymentMethodsList"></div>
                </div>
            </div>

            <!-- Payment Test -->
            <div class="test-section">
                <h3><i class="fas fa-vial"></i> اختبار طرق الدفع</h3>
                <p>اختبار تكوين وإعدادات طرق الدفع</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-warning" onclick="testPaymentMethods()">
                        <i class="fas fa-flask"></i>
                        اختبار الطرق
                    </button>
                </div>

                <div class="loading" id="testLoading">
                    <div class="spinner"></div>
                    <p>جاري اختبار طرق الدفع...</p>
                </div>

                <div class="results" id="testResults" style="display: none;">
                    <h4>نتائج اختبار طرق الدفع</h4>
                    <div id="testResultsList"></div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> إحصائيات الدفع</h3>
                <p>عرض إحصائيات وتحليلات طرق الدفع</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="loadPaymentStats()">
                        <i class="fas fa-chart-line"></i>
                        تحميل الإحصائيات
                    </button>
                </div>

                <div class="loading" id="statsLoading">
                    <div class="spinner"></div>
                    <p>جاري تحميل الإحصائيات...</p>
                </div>

                <div class="results" id="statsResults" style="display: none;">
                    <h4>إحصائيات طرق الدفع</h4>
                    <div id="statsResultsList"></div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <a href="admin/system-settings/payment-settings.html" class="btn btn-primary" target="_blank">
                <i class="fas fa-credit-card"></i>
                إعدادات الدفع
            </a>
            <a href="admin/system-settings/index.html" class="btn btn-success" target="_blank">
                <i class="fas fa-cogs"></i>
                إعدادات النظام
            </a>
            <a href="database/setup-test.html" class="btn btn-warning" target="_blank">
                <i class="fas fa-database"></i>
                إعداد قاعدة البيانات
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'api/system/payment-settings.php';

        // Test Payment API
        async function testPaymentAPI() {
            const loading = document.getElementById('apiLoading');
            const results = document.getElementById('apiResults');
            const resultsList = document.getElementById('apiResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            const endpoints = [
                { name: 'جلب طرق الدفع', url: `${API_BASE}`, method: 'GET' },
                { name: 'إحصائيات الدفع', url: `${API_BASE}?action=stats`, method: 'GET' },
                { name: 'اختبار طريقة دفع', url: `${API_BASE}?action=test&method_id=1`, method: 'GET' }
            ];
            
            resultsList.innerHTML = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    const data = await response.json();
                    
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status ${data.success ? 'success' : 'error'}">
                            ${data.success ? 'يعمل' : 'خطأ'}
                        </span>
                    `;
                    resultsList.appendChild(item);
                    
                } catch (error) {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status error">فشل</span>
                    `;
                    resultsList.appendChild(item);
                }
            }
            
            loading.classList.remove('show');
            results.style.display = 'block';
        }

        // Load Payment Methods
        async function loadPaymentMethods() {
            const loading = document.getElementById('methodsLoading');
            const demo = document.getElementById('paymentDemo');
            const methodsList = document.getElementById('paymentMethodsList');
            
            loading.classList.add('show');
            demo.style.display = 'none';
            
            try {
                const response = await fetch(API_BASE);
                const data = await response.json();
                
                if (data.success && data.data) {
                    methodsList.innerHTML = '';
                    
                    data.data.forEach(method => {
                        const methodElement = document.createElement('div');
                        methodElement.className = 'payment-method';
                        methodElement.innerHTML = `
                            <div class="payment-icon ${method.type}">
                                <i class="${getPaymentIcon(method.type)}"></i>
                            </div>
                            <div class="payment-info">
                                <h4>${method.name_ar}</h4>
                                <p>${method.name} ${method.provider ? `- ${method.provider}` : ''}</p>
                            </div>
                            <div class="payment-fees">
                                <div>رسوم: ${method.fees_percentage}% + ${method.fees_fixed} دج</div>
                                <div>الحد الأدنى: ${method.min_amount} دج</div>
                            </div>
                        `;
                        methodsList.appendChild(methodElement);
                    });
                    
                    demo.style.display = 'block';
                } else {
                    showNotification('فشل في تحميل طرق الدفع', 'error');
                }
                
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Test Payment Methods
        async function testPaymentMethods() {
            const loading = document.getElementById('testLoading');
            const results = document.getElementById('testResults');
            const resultsList = document.getElementById('testResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            try {
                // First get all payment methods
                const methodsResponse = await fetch(API_BASE);
                const methodsData = await methodsResponse.json();
                
                if (methodsData.success && methodsData.data) {
                    resultsList.innerHTML = '';
                    
                    for (const method of methodsData.data) {
                        try {
                            const testResponse = await fetch(`${API_BASE}?action=test&method_id=${method.id}`);
                            const testData = await testResponse.json();
                            
                            const item = document.createElement('div');
                            item.className = 'result-item';
                            
                            if (testData.success) {
                                const overall = testData.data.test_results.overall;
                                const statusClass = overall.status === 'passed' ? 'success' : 'error';
                                const statusText = overall.status === 'passed' ? 'نجح' : 'فشل';
                                
                                item.innerHTML = `
                                    <span><strong>${method.name_ar}</strong> - ${overall.percentage}%</span>
                                    <span class="status ${statusClass}">${statusText}</span>
                                `;
                            } else {
                                item.innerHTML = `
                                    <span><strong>${method.name_ar}</strong></span>
                                    <span class="status error">خطأ</span>
                                `;
                            }
                            
                            resultsList.appendChild(item);
                            
                        } catch (error) {
                            const item = document.createElement('div');
                            item.className = 'result-item';
                            item.innerHTML = `
                                <span><strong>${method.name_ar}</strong></span>
                                <span class="status error">فشل</span>
                            `;
                            resultsList.appendChild(item);
                        }
                    }
                    
                    results.style.display = 'block';
                }
                
            } catch (error) {
                showNotification('فشل في اختبار طرق الدفع: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Load Payment Stats
        async function loadPaymentStats() {
            const loading = document.getElementById('statsLoading');
            const results = document.getElementById('statsResults');
            const resultsList = document.getElementById('statsResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            try {
                const response = await fetch(`${API_BASE}?action=stats`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    resultsList.innerHTML = '';
                    
                    // Total methods
                    const totalItem = document.createElement('div');
                    totalItem.className = 'result-item';
                    totalItem.innerHTML = `
                        <span><strong>إجمالي طرق الدفع</strong></span>
                        <span class="status success">${stats.total}</span>
                    `;
                    resultsList.appendChild(totalItem);
                    
                    // Active methods
                    const activeItem = document.createElement('div');
                    activeItem.className = 'result-item';
                    activeItem.innerHTML = `
                        <span><strong>الطرق النشطة</strong></span>
                        <span class="status success">${stats.active}</span>
                    `;
                    resultsList.appendChild(activeItem);
                    
                    // Average fees
                    const feesItem = document.createElement('div');
                    feesItem.className = 'result-item';
                    feesItem.innerHTML = `
                        <span><strong>متوسط الرسوم</strong></span>
                        <span class="status warning">${stats.average_fees.percentage}% + ${stats.average_fees.fixed} دج</span>
                    `;
                    resultsList.appendChild(feesItem);
                    
                    // Methods by type
                    if (stats.by_type) {
                        Object.entries(stats.by_type).forEach(([type, count]) => {
                            const typeItem = document.createElement('div');
                            typeItem.className = 'result-item';
                            typeItem.innerHTML = `
                                <span><strong>${getPaymentTypeName(type)}</strong></span>
                                <span class="status success">${count}</span>
                            `;
                            resultsList.appendChild(typeItem);
                        });
                    }
                    
                    results.style.display = 'block';
                } else {
                    showNotification('فشل في تحميل الإحصائيات', 'error');
                }
                
            } catch (error) {
                showNotification('خطأ في تحميل الإحصائيات: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Helper Functions
        function getPaymentIcon(type) {
            const icons = {
                'credit_card': 'fas fa-credit-card',
                'bank_transfer': 'fas fa-university',
                'cash_on_delivery': 'fas fa-money-bill-wave',
                'digital_wallet': 'fas fa-wallet',
                'cryptocurrency': 'fab fa-bitcoin'
            };
            return icons[type] || 'fas fa-credit-card';
        }

        function getPaymentTypeName(type) {
            const names = {
                'credit_card': 'بطاقة ائتمان',
                'bank_transfer': 'تحويل بنكي',
                'cash_on_delivery': 'الدفع عند الاستلام',
                'digital_wallet': 'محفظة رقمية',
                'cryptocurrency': 'عملة رقمية'
            };
            return names[type] || type;
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
