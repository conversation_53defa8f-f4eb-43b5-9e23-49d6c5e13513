<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - صفحة مستقلة</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Users Management CSS -->
    <link rel="stylesheet" href="css/users-management.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .main-content {
            min-height: calc(100vh - 120px);
            padding: 20px 0;
        }
        
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
            flex-direction: column;
            gap: 20px;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #dc3545;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.7;
        }
        
        .error-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #dc3545;
        }
        
        .error-message {
            margin-bottom: 25px;
            color: #666;
            font-size: 1.1rem;
        }
        
        .error-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .error-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .error-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .error-btn.success {
            background: #28a745;
            color: white;
        }
        
        .error-btn.info {
            background: #17a2b8;
            color: white;
        }
        
        .error-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .back-link {
                position: static;
                display: inline-block;
                margin: 10px;
            }
            
            .page-header {
                padding: 15px;
            }
            
            .page-header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back Link -->
    <a href="index.html" class="back-link">
        <i class="fas fa-arrow-left"></i> العودة للوحة الإدارة
    </a>
    
    <!-- Page Header -->
    <div class="page-header">
        <h1><i class="fas fa-users"></i> إدارة المستخدمين</h1>
        <p>إدارة وتنظيم جميع مستخدمي النظام والصلاحيات - صفحة مستقلة</p>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div id="usersManagementContent">
            <!-- Content will be loaded here -->
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p style="color: #666; font-size: 1.1rem;">جاري تحميل إدارة المستخدمين...</p>
                <p style="color: #999; font-size: 0.9rem;">يتم الآن جلب البيانات من الخادم...</p>
            </div>
        </div>
    </div>
    
    <!-- Users Management JavaScript -->
    <script src="js/users-management.js"></script>
    
    <script>
        console.log('👥 تحميل صفحة إدارة المستخدمين المستقلة...');
        
        // Auto-load users when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 تم تحميل الصفحة، بدء تحميل المستخدمين...');
            
            // Small delay to ensure all scripts are loaded
            setTimeout(() => {
                if (typeof loadUsersManagementContent === 'function') {
                    console.log('✅ دالة التحميل متاحة، بدء التحميل...');
                    loadUsersManagementContent();
                } else {
                    console.error('❌ دالة التحميل غير متاحة');
                    showManualError();
                }
            }, 500);
        });
        
        // Manual error display if function not available
        function showManualError() {
            const container = document.getElementById('usersManagementContent');
            container.innerHTML = `
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="error-title">خطأ في تحميل إدارة المستخدمين</h3>
                    <p class="error-message">لم يتم العثور على دالة التحميل. تأكد من تحميل ملف JavaScript الصحيح.</p>
                    <div class="error-actions">
                        <button class="error-btn primary" onclick="location.reload()">
                            <i class="fas fa-redo"></i> إعادة تحميل الصفحة
                        </button>
                        <a href="php/users_management.php?action=get_all" target="_blank" class="error-btn success">
                            <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                        </a>
                        <a href="setup/users_management_tables.php" target="_blank" class="error-btn info">
                            <i class="fas fa-database"></i> إعداد قاعدة البيانات
                        </a>
                    </div>
                </div>
            `;
        }
        
        // Test API connection
        async function testAPIConnection() {
            console.log('🔍 اختبار اتصال API...');
            
            try {
                const response = await fetch('php/users_management.php?action=get_all');
                const data = await response.json();
                
                console.log('📡 نتيجة اختبار API:', data);
                
                if (data.success) {
                    console.log('✅ API يعمل بشكل صحيح');
                    return true;
                } else {
                    console.error('❌ API يعيد خطأ:', data.message);
                    return false;
                }
            } catch (error) {
                console.error('❌ خطأ في اتصال API:', error);
                return false;
            }
        }
        
        // Enhanced error handling
        window.addEventListener('error', function(e) {
            console.error('❌ خطأ JavaScript:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('❌ خطأ Promise:', e.reason);
        });
        
        console.log('✅ تم تحميل صفحة إدارة المستخدمين المستقلة');
    </script>
</body>
</html>
