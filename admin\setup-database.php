<?php

/**
 * Database Setup Script for Enhanced Admin System
 * Creates all required tables for the dashboard and admin functionality
 */

require_once '../config/database.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - مصعب لاندينغ بيج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }

        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }

        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🗄️ إعداد قاعدة البيانات</h1>

        <?php
        try {
            $db = Database::getInstance();
            echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

            // Step 1: Create produits table
            echo "<div class='step'>";
            echo "<h3>📦 إنشاء جدول المنتجات (produits)</h3>";

            $createProduits = "
            CREATE TABLE IF NOT EXISTS produits (
                id INT AUTO_INCREMENT PRIMARY KEY,
                titre VARCHAR(255) NOT NULL,
                description TEXT,
                prix DECIMAL(10,2) NOT NULL,
                stock INT DEFAULT 0,
                type VARCHAR(50) DEFAULT 'book',
                category_id INT DEFAULT NULL,
                image_url VARCHAR(500),
                auteur VARCHAR(255),
                processeur VARCHAR(255),
                ram VARCHAR(100),
                stockage VARCHAR(100),
                materiel TEXT,
                capacite VARCHAR(100),
                actif TINYINT(1) DEFAULT 1,
                has_landing_page TINYINT(1) DEFAULT 0,
                landing_page_enabled TINYINT(1) DEFAULT 0,
                slug VARCHAR(255),
                meta_title VARCHAR(255),
                meta_description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category_id (category_id),
                INDEX idx_actif (actif),
                INDEX idx_type (type),
                INDEX idx_slug (slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createProduits);
            echo "<div class='success'>✅ تم إنشاء جدول المنتجات بنجاح</div>";
            echo "</div>";

            // Step 2: Create commandes table
            echo "<div class='step'>";
            echo "<h3>🛒 إنشاء جدول الطلبات (commandes)</h3>";

            $createCommandes = "
            CREATE TABLE IF NOT EXISTS commandes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(255) NOT NULL,
                prenom VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                telephone VARCHAR(20) NOT NULL,
                adresse TEXT NOT NULL,
                wilaya VARCHAR(100) NOT NULL,
                commune VARCHAR(100) NOT NULL,
                montant_total DECIMAL(10,2) NOT NULL,
                statut ENUM('en_attente', 'confirmé', 'payé', 'expédié', 'livré', 'annulé') DEFAULT 'en_attente',
                mode_paiement ENUM('cod', 'ccp', 'baridimob', 'bank_transfer') DEFAULT 'cod',
                notes TEXT,
                date_commande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_statut (statut),
                INDEX idx_date_commande (date_commande),
                INDEX idx_wilaya (wilaya)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createCommandes);
            echo "<div class='success'>✅ تم إنشاء جدول الطلبات بنجاح</div>";
            echo "</div>";

            // Step 3: Create details_commande table
            echo "<div class='step'>";
            echo "<h3>📋 إنشاء جدول تفاصيل الطلبات (details_commande)</h3>";

            $createDetailsCommande = "
            CREATE TABLE IF NOT EXISTS details_commande (
                id INT AUTO_INCREMENT PRIMARY KEY,
                commande_id INT NOT NULL,
                livre_id INT NOT NULL,
                quantite INT NOT NULL DEFAULT 1,
                prix_unitaire DECIMAL(10,2) NOT NULL,
                sous_total DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (commande_id) REFERENCES commandes(id) ON DELETE CASCADE,
                FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE CASCADE,
                INDEX idx_commande_id (commande_id),
                INDEX idx_livre_id (livre_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createDetailsCommande);
            echo "<div class='success'>✅ تم إنشاء جدول تفاصيل الطلبات بنجاح</div>";
            echo "</div>";

            // Step 4: Create landing_pages table
            echo "<div class='step'>";
            echo "<h3>🌐 إنشاء جدول صفحات الهبوط (landing_pages)</h3>";

            $createLandingPages = "
            CREATE TABLE IF NOT EXISTS landing_pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                titre VARCHAR(255) NOT NULL,
                produit_id INT NOT NULL,
                template_id VARCHAR(50) NOT NULL DEFAULT 'modern',
                contenu_droit TEXT,
                contenu_gauche TEXT,
                image_position ENUM('left', 'right', 'center') DEFAULT 'center',
                text_position ENUM('left', 'right', 'split') DEFAULT 'split',
                meta_description TEXT,
                meta_keywords TEXT,
                lien_url VARCHAR(500),
                actif TINYINT(1) DEFAULT 1,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE,
                INDEX idx_produit_id (produit_id),
                INDEX idx_actif (actif)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createLandingPages);
            echo "<div class='success'>✅ تم إنشاء جدول صفحات الهبوط بنجاح</div>";
            echo "</div>";

            // Step 5: Create categories table
            echo "<div class='step'>";
            echo "<h3>🏷️ إنشاء جدول الفئات (categories)</h3>";

            $createCategories = "
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom_ar VARCHAR(255) NOT NULL,
                nom_en VARCHAR(255),
                description_ar TEXT,
                description_en TEXT,
                icone VARCHAR(100) DEFAULT 'fas fa-tag',
                couleur VARCHAR(7) DEFAULT '#007bff',
                ordre INT DEFAULT 0,
                actif TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_ordre (ordre),
                INDEX idx_actif (actif)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createCategories);
            echo "<div class='success'>✅ تم إنشاء جدول الفئات بنجاح</div>";
            echo "</div>";

            // Step 6: Create store_settings table
            echo "<div class='step'>";
            echo "<h3>⚙️ إنشاء جدول إعدادات المتجر (store_settings)</h3>";

            $createStoreSettings = "
            CREATE TABLE IF NOT EXISTS store_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_setting_key (setting_key),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createStoreSettings);
            echo "<div class='success'>✅ تم إنشاء جدول إعدادات المتجر بنجاح</div>";
            echo "</div>";

            // Step 7: Insert sample data if tables are empty
            echo "<div class='step'>";
            echo "<h3>📊 إضافة بيانات تجريبية</h3>";

            // Check if produits table is empty
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM produits");
            $stmt->execute();
            $productCount = $stmt->fetch()['count'];

            if ($productCount == 0) {
                echo "<div class='info'>إضافة منتجات تجريبية...</div>";

                $sampleProducts = [
                    ['كتاب البرمجة للمبتدئين', 'كتاب شامل لتعلم البرمجة من الصفر', 2500.00, 50, 'book', 'أحمد محمد'],
                    ['حاسوب محمول للمطورين', 'حاسوب عالي الأداء للمطورين والمصممين', 85000.00, 10, 'laptop', null],
                    ['حقيبة ظهر ذكية', 'حقيبة ظهر مع شاحن USB مدمج', 4500.00, 25, 'bag', null]
                ];

                $insertProduct = $db->prepare("INSERT INTO produits (titre, description, prix, stock, type, auteur) VALUES (?, ?, ?, ?, ?, ?)");

                foreach ($sampleProducts as $product) {
                    $insertProduct->execute($product);
                }

                echo "<div class='success'>✅ تم إضافة " . count($sampleProducts) . " منتج تجريبي</div>";
            } else {
                echo "<div class='info'>يوجد $productCount منتج في قاعدة البيانات</div>";
            }

            // Check if categories table is empty
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM categories");
            $stmt->execute();
            $categoryCount = $stmt->fetch()['count'];

            if ($categoryCount == 0) {
                echo "<div class='info'>إضافة فئات تجريبية...</div>";

                $sampleCategories = [
                    ['الكتب', 'Books', 'كتب ومراجع متنوعة', 'Various books and references', 'fas fa-book', '#007bff', 1],
                    ['الحاسوب', 'Computers', 'أجهزة حاسوب ولوازمها', 'Computers and accessories', 'fas fa-laptop', '#28a745', 2],
                    ['الحقائب', 'Bags', 'حقائب وأكياس متنوعة', 'Various bags and cases', 'fas fa-shopping-bag', '#ffc107', 3]
                ];

                $insertCategory = $db->prepare("INSERT INTO categories (nom_ar, nom_en, description_ar, description_en, icone, couleur, ordre) VALUES (?, ?, ?, ?, ?, ?, ?)");

                foreach ($sampleCategories as $category) {
                    $insertCategory->execute($category);
                }

                echo "<div class='success'>✅ تم إضافة " . count($sampleCategories) . " فئة تجريبية</div>";
            } else {
                echo "<div class='info'>يوجد $categoryCount فئة في قاعدة البيانات</div>";
            }

            echo "</div>";

            // Step 8: Create multi-user system tables
            echo "<div class='step'>";
            echo "<h3>👥 إنشاء جداول النظام متعدد المستخدمين</h3>";

            // Check if users table already exists
            $checkUsers = $db->query("SHOW TABLES LIKE 'users'");
            if ($checkUsers->rowCount() > 0) {
                echo "<div class='info'>ℹ️ جدول المستخدمين (users) موجود بالفعل - تم تخطي الإنشاء</div>";

                // Verify table structure
                $stmt = $db->query("SELECT COUNT(*) as count FROM users");
                $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                echo "<div class='info'>📊 عدد المستخدمين الحاليين: $userCount</div>";
            } else {
                // Create users table only if it doesn't exist
                $createUsers = "
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email VARCHAR(100) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    first_name VARCHAR(100),
                    last_name VARCHAR(100),
                    phone VARCHAR(20),
                    role_id INT DEFAULT 4,
                    subscription_id INT DEFAULT 1,
                    store_id INT NULL,
                    avatar VARCHAR(500),
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    email_verified TINYINT(1) DEFAULT 0,
                    last_login TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_username (username),
                    INDEX idx_email (email),
                    INDEX idx_role_id (role_id),
                    INDEX idx_subscription_id (subscription_id),
                    INDEX idx_store_id (store_id),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                $db->exec($createUsers);
                echo "<div class='success'>✅ تم إنشاء جدول المستخدمين (users)</div>";
            }

            // Create user_roles table
            $createUserRoles = "
            CREATE TABLE IF NOT EXISTS user_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                display_name_ar VARCHAR(100) NOT NULL,
                display_name_en VARCHAR(100) NOT NULL,
                description TEXT,
                permissions JSON,
                level INT DEFAULT 1,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_level (level),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createUserRoles);
            echo "<div class='success'>✅ تم إنشاء جدول أدوار المستخدمين (user_roles)</div>";

            // Create subscription_plans table
            $createSubscriptionPlans = "
            CREATE TABLE IF NOT EXISTS subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                display_name_ar VARCHAR(100) NOT NULL,
                display_name_en VARCHAR(100) NOT NULL,
                description_ar TEXT,
                description_en TEXT,
                price DECIMAL(10,2) DEFAULT 0.00,
                currency VARCHAR(3) DEFAULT 'DZD',
                duration_days INT DEFAULT 30,
                max_products INT DEFAULT 5,
                max_landing_pages INT DEFAULT 2,
                max_storage_mb INT DEFAULT 100,
                max_templates INT DEFAULT 5,
                features JSON,
                is_active TINYINT(1) DEFAULT 1,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_is_active (is_active),
                INDEX idx_sort_order (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createSubscriptionPlans);
            echo "<div class='success'>✅ تم إنشاء جدول خطط الاشتراك (subscription_plans)</div>";

            // Create user_stores table
            $createUserStores = "
            CREATE TABLE IF NOT EXISTS user_stores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                store_name VARCHAR(255) NOT NULL,
                store_slug VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                logo VARCHAR(500),
                settings JSON,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_store_slug (store_slug),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createUserStores);
            echo "<div class='success'>✅ تم إنشاء جدول متاجر المستخدمين (user_stores)</div>";

            echo "</div>";

            // Step 9: Insert default roles and subscription plans
            echo "<div class='step'>";
            echo "<h3>📊 إضافة البيانات الافتراضية</h3>";

            // Insert default roles
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_roles");
            $stmt->execute();
            $roleCount = $stmt->fetch()['count'];

            if ($roleCount == 0) {
                echo "<div class='info'>إضافة الأدوار الافتراضية...</div>";

                $defaultRoles = [
                    [1, 'super_admin', 'مدير عام', 'Super Admin', 'صلاحيات كاملة للنظام', '["*"]', 100],
                    [2, 'admin', 'مدير', 'Admin', 'إدارة المستخدمين والمنتجات والطلبات', '["users", "products", "orders", "settings"]', 80],
                    [3, 'store_owner', 'صاحب متجر', 'Store Owner', 'إدارة المتجر الشخصي والمنتجات', '["own_store", "own_products", "own_orders"]', 60],
                    [4, 'customer', 'عميل', 'Customer', 'تصفح المنتجات وإجراء الطلبات', '["browse", "order"]', 20]
                ];

                $insertRole = $db->prepare("INSERT INTO user_roles (id, name, display_name_ar, display_name_en, description, permissions, level) VALUES (?, ?, ?, ?, ?, ?, ?)");

                foreach ($defaultRoles as $role) {
                    $insertRole->execute($role);
                }

                echo "<div class='success'>✅ تم إضافة " . count($defaultRoles) . " دور افتراضي</div>";
            } else {
                echo "<div class='info'>يوجد $roleCount دور في قاعدة البيانات</div>";
            }

            // Insert default subscription plans
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM subscription_plans");
            $stmt->execute();
            $planCount = $stmt->fetch()['count'];

            if ($planCount == 0) {
                echo "<div class='info'>إضافة خطط الاشتراك الافتراضية...</div>";

                $defaultPlans = [
                    [1, 'free', 'مجاني', 'Free', 'خطة مجانية للمبتدئين', 'Free plan for beginners', 0.00, 'DZD', 365, 5, 2, 100, 5, '["basic_templates", "email_support"]'],
                    [2, 'basic', 'أساسي', 'Basic', 'خطة أساسية للأعمال الصغيرة', 'Basic plan for small businesses', 2500.00, 'DZD', 30, 50, 10, 1024, 15, '["premium_templates", "email_support", "analytics"]'],
                    [3, 'premium', 'مميز', 'Premium', 'خطة مميزة للأعمال المتوسطة', 'Premium plan for medium businesses', 5000.00, 'DZD', 30, 500, 50, 5120, 50, '["all_templates", "priority_support", "advanced_analytics", "custom_domain"]'],
                    [4, 'unlimited', 'غير محدود', 'Unlimited', 'خطة غير محدودة للمؤسسات', 'Unlimited plan for enterprises', 10000.00, 'DZD', 30, -1, -1, -1, -1, '["everything", "24_7_support", "white_label", "api_access"]']
                ];

                $insertPlan = $db->prepare("INSERT INTO subscription_plans (id, name, display_name_ar, display_name_en, description_ar, description_en, price, currency, duration_days, max_products, max_landing_pages, max_storage_mb, max_templates, features) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                foreach ($defaultPlans as $plan) {
                    $insertPlan->execute($plan);
                }

                echo "<div class='success'>✅ تم إضافة " . count($defaultPlans) . " خطة اشتراك افتراضية</div>";
            } else {
                echo "<div class='info'>يوجد $planCount خطة اشتراك في قاعدة البيانات</div>";
            }

            echo "</div>";

            // Final verification
            echo "<div class='step'>";
            echo "<h3>✅ التحقق النهائي</h3>";

            $tables = ['produits', 'commandes', 'details_commande', 'landing_pages', 'categories', 'store_settings', 'users', 'user_roles', 'subscription_plans', 'user_stores'];

            foreach ($tables as $table) {
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
                $stmt->execute();
                $count = $stmt->fetch()['count'];
                echo "<div class='success'>✅ جدول $table: $count سجل</div>";
            }

            echo "</div>";

            echo "<div class='success'>";
            echo "<h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
            echo "<p>يمكنك الآن استخدام لوحة الإدارة بكامل ميزاتها.</p>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إعداد قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="btn">🏠 العودة إلى لوحة الإدارة</a>
            <a href="test-dashboard-api.php" class="btn">🧪 اختبار API لوحة المعلومات</a>
        </div>
    </div>
</body>

</html>
