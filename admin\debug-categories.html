<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تشخيص إدارة الفئات - محدث</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #f8f9fa;
      }

      .test-btn {
        padding: 10px 20px;
        margin: 5px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        color: white;
        background: #667eea;
        transition: all 0.3s ease;
      }

      .test-btn:hover {
        background: #5a67d8;
        transform: translateY(-2px);
      }

      .result {
        margin-top: 15px;
        padding: 15px;
        border-radius: 6px;
        background: #ffffff;
        border: 1px solid #dee2e6;
        max-height: 400px;
        overflow-y: auto;
      }

      .success {
        border-color: #28a745;
        background: #d4edda;
        color: #155724;
      }
      .error {
        border-color: #dc3545;
        background: #f8d7da;
        color: #721c24;
      }
      .loading {
        border-color: #17a2b8;
        background: #d1ecf1;
        color: #0c5460;
      }

      pre {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 0.9em;
      }

      #categoriesManagementContent {
        margin-top: 20px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        min-height: 200px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1><i class="fas fa-bug"></i> تشخيص إدارة الفئات</h1>
      <p>تشخيص شامل لمشكلة عدم تحميل إدارة الفئات</p>

      <!-- Test 1: API Direct -->
      <div class="test-section">
        <h3><i class="fas fa-server"></i> اختبار 1: API مباشر</h3>
        <button class="test-btn" onclick="testAPIDirect()">
          <i class="fas fa-play"></i> اختبار API مباشر
        </button>
        <div id="apiDirectResult" class="result" style="display: none"></div>
      </div>

      <!-- Test 2: JavaScript Loading -->
      <div class="test-section">
        <h3><i class="fas fa-code"></i> اختبار 2: تحميل JavaScript</h3>
        <button class="test-btn" onclick="testJSLoading()">
          <i class="fas fa-play"></i> اختبار تحميل JavaScript
        </button>
        <div id="jsLoadingResult" class="result" style="display: none"></div>
      </div>

      <!-- Test 3: Manual Load -->
      <div class="test-section">
        <h3><i class="fas fa-hand-paper"></i> اختبار 3: تحميل يدوي</h3>
        <button class="test-btn" onclick="manualLoad()">
          <i class="fas fa-play"></i> تحميل يدوي للفئات
        </button>
        <div id="manualLoadResult" class="result" style="display: none"></div>
      </div>

      <!-- Categories Container -->
      <div class="test-section">
        <h3><i class="fas fa-sitemap"></i> حاوي إدارة الفئات</h3>
        <div id="categoriesManagementContent">
          <div style="text-align: center; padding: 40px; color: #666">
            <i class="fas fa-clock"></i> في انتظار التحميل...
          </div>
        </div>
      </div>
    </div>

    <script>
      console.log("🔧 بدء تشخيص إدارة الفئات...");

      // Test 1: API Direct
      async function testAPIDirect() {
        const resultDiv = document.getElementById("apiDirectResult");
        resultDiv.style.display = "block";
        resultDiv.className = "result loading";
        resultDiv.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري اختبار API...';

        try {
          console.log("🌐 اختبار API مباشر...");
          const response = await fetch("php/categories.php?action=get_all");
          console.log("📡 استجابة API:", response);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log("📦 بيانات API:", data);

          if (data.success) {
            resultDiv.className = "result success";
            resultDiv.innerHTML = `
                        <h4><i class="fas fa-check-circle"></i> نجح اختبار API</h4>
                        <p>تم جلب ${data.data.total} فئة بنجاح</p>
                        <p>الفئات الرئيسية: ${
                          data.data.categories.filter(
                            (c) => c.parent_id === null
                          ).length
                        }</p>
                        <p>الفئات الفرعية: ${
                          data.data.categories.filter(
                            (c) => c.parent_id !== null
                          ).length
                        }</p>
                        <details>
                            <summary>عرض البيانات الكاملة</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
          } else {
            throw new Error(data.message || "فشل في جلب البيانات");
          }
        } catch (error) {
          console.error("❌ خطأ في API:", error);
          resultDiv.className = "result error";
          resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> فشل اختبار API</h4>
                    <p>خطأ: ${error.message}</p>
                `;
        }
      }

      // Test 2: JavaScript Loading
      function testJSLoading() {
        const resultDiv = document.getElementById("jsLoadingResult");
        resultDiv.style.display = "block";

        console.log("🔧 اختبار تحميل JavaScript...");

        const tests = [
          {
            name: "وجود window.CategoriesManager",
            test: () => typeof window.CategoriesManager === "object",
          },
          {
            name: "وجود loadCategoriesManagementContent",
            test: () =>
              typeof window.loadCategoriesManagementContent === "function",
          },
          {
            name: "وجود CategoriesManager.loadContent",
            test: () =>
              window.CategoriesManager &&
              typeof window.CategoriesManager.loadContent === "function",
          },
          {
            name: "وجود CategoriesManager.fetchCategories",
            test: () =>
              window.CategoriesManager &&
              typeof window.CategoriesManager.fetchCategories === "function",
          },
        ];

        let results =
          '<h4><i class="fas fa-code"></i> نتائج اختبار JavaScript</h4>';
        let allPassed = true;

        tests.forEach((test) => {
          const passed = test.test();
          allPassed = allPassed && passed;
          const icon = passed
            ? '<i class="fas fa-check" style="color: green;"></i>'
            : '<i class="fas fa-times" style="color: red;"></i>';
          results += `<p>${icon} ${test.name}: ${passed ? "نجح" : "فشل"}</p>`;
          console.log(
            `${passed ? "✅" : "❌"} ${test.name}: ${passed ? "نجح" : "فشل"}`
          );
        });

        resultDiv.className = allPassed ? "result success" : "result error";
        resultDiv.innerHTML = results;
      }

      // Test 3: Manual Load
      async function manualLoad() {
        const resultDiv = document.getElementById("manualLoadResult");
        resultDiv.style.display = "block";
        resultDiv.className = "result loading";
        resultDiv.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> جاري التحميل اليدوي...';

        console.log("🔧 بدء التحميل اليدوي...");

        try {
          // Step 1: Check container
          const container = document.getElementById(
            "categoriesManagementContent"
          );
          if (!container) {
            throw new Error("حاوي categoriesManagementContent غير موجود");
          }
          console.log("✅ تم العثور على الحاوي");

          // Step 2: Show loading
          container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
                    </div>
                `;
          console.log("✅ تم عرض حالة التحميل");

          // Step 3: Fetch data
          const response = await fetch("php/categories.php?action=get_all");
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log("✅ تم جلب البيانات:", data);

          if (!data.success) {
            throw new Error(data.message || "فشل في جلب البيانات");
          }

          // Step 4: Render interface
          const categories = data.data.categories;
          const mainCategories = categories.filter((c) => c.parent_id === null);
          const subCategories = categories.filter((c) => c.parent_id !== null);

          container.innerHTML = `
                    <div style="max-width: 1200px; margin: 0 auto;">
                        <!-- Header -->
                        <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; margin-bottom: 20px;">
                            <h2 style="margin: 0;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">تم التحميل بنجاح!</p>
                        </div>

                        <!-- Statistics -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"><i class="fas fa-folder"></i></div>
                                <h3 style="margin: 0; font-size: 2rem; color: #333;">${
                                  data.data.total
                                }</h3>
                                <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                            </div>
                            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2rem; color: #28a745; margin-bottom: 10px;"><i class="fas fa-folder-open"></i></div>
                                <h3 style="margin: 0; font-size: 2rem; color: #333;">${
                                  mainCategories.length
                                }</h3>
                                <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                            </div>
                            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;"><i class="fas fa-layer-group"></i></div>
                                <h3 style="margin: 0; font-size: 2rem; color: #333;">${
                                  subCategories.length
                                }</h3>
                                <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                            </div>
                        </div>

                        <!-- Categories List -->
                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 20px;">
                            <h3 style="margin: 0 0 20px 0; color: #333;"><i class="fas fa-list"></i> قائمة الفئات</h3>
                            ${renderCategoriesList(categories)}
                        </div>
                    </div>
                `;

          console.log("✅ تم رسم الواجهة بنجاح");

          resultDiv.className = "result success";
          resultDiv.innerHTML = `
                    <h4><i class="fas fa-check-circle"></i> نجح التحميل اليدوي</h4>
                    <p>تم تحميل وعرض ${data.data.total} فئة بنجاح</p>
                    <p>تحقق من حاوي إدارة الفئات أدناه</p>
                `;
        } catch (error) {
          console.error("❌ خطأ في التحميل اليدوي:", error);

          resultDiv.className = "result error";
          resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> فشل التحميل اليدوي</h4>
                    <p>خطأ: ${error.message}</p>
                `;

          // Show error in container too
          const container = document.getElementById(
            "categoriesManagementContent"
          );
          if (container) {
            container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h4>خطأ في التحميل</h4>
                            <p>${error.message}</p>
                        </div>
                    `;
          }
        }
      }

      // Helper function to render categories list
      function renderCategoriesList(categories) {
        let html = "";

        categories.forEach((category) => {
          const isMain = category.parent_id === null;
          const indent = isMain ? "0px" : "20px";
          const bgColor = isMain ? "#f8f9fa" : "#ffffff";
          const borderColor = isMain ? "#667eea" : "#e0e0e0";

          html += `
                    <div style="margin-right: ${indent}; margin-bottom: 10px; padding: 15px; border: 1px solid ${borderColor}; border-radius: 8px; background: ${bgColor};">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="color: ${
                              category.color
                            }; font-size: 1.5rem;">
                                <i class="${
                                  category.icon || "fas fa-folder"
                                }"></i>
                            </div>
                            <div style="flex: 1;">
                                <h4 style="margin: 0 0 5px 0; color: #333;">
                                    ${category.name_ar}
                                    ${
                                      category.is_featured == 1
                                        ? '<span style="color: #ffc107;">⭐</span>'
                                        : ""
                                    }
                                </h4>
                                <p style="margin: 0; color: #666; font-size: 0.9em;">${
                                  category.description_ar || "لا يوجد وصف"
                                }</p>
                            </div>
                            <div style="text-align: center; font-size: 0.8em; color: #999;">
                                <div>${
                                  category.subcategories_count || 0
                                } فئة فرعية</div>
                                <div>${category.products_count || 0} منتج</div>
                            </div>
                        </div>
                    </div>
                `;
        });

        return (
          html ||
          '<p style="text-align: center; color: #666; padding: 20px;">لا توجد فئات للعرض</p>'
        );
      }

      // Auto-run tests on page load
      document.addEventListener("DOMContentLoaded", function () {
        console.log("🧪 بدء الاختبارات التلقائية...");
        setTimeout(() => {
          testJSLoading();
        }, 1000);
      });
    </script>
  </body>
</html>
