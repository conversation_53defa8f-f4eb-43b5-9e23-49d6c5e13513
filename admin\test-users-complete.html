<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لإدارة المستخدمين</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-users"></i> اختبار شامل لإدارة المستخدمين</h1>
        <p>هذا الاختبار يتحقق من جميع وظائف نظام إدارة المستخدمين المطور حديثاً</p>
        
        <!-- Database Connection Test -->
        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبار الاتصال بقاعدة البيانات</h3>
            <p>التحقق من إعداد جداول المستخدمين والاتصال بقاعدة البيانات</p>
            
            <button class="btn-primary" onclick="testDatabaseConnection()">1. اختبار الاتصال</button>
            <button class="btn-success" onclick="testUsersAPI()">2. اختبار API المستخدمين</button>
            <button class="btn-warning" onclick="testRolesAPI()">3. اختبار API الأدوار</button>
            <button class="btn-info" onclick="testPermissionsAPI()">4. اختبار API الصلاحيات</button>
            
            <div id="dbResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Users Management Test -->
        <div class="test-section">
            <h3><i class="fas fa-user-cog"></i> اختبار إدارة المستخدمين</h3>
            <p>اختبار وظائف إضافة وتعديل وحذف المستخدمين</p>
            
            <button class="btn-primary" onclick="testCreateUser()">1. إنشاء مستخدم تجريبي</button>
            <button class="btn-warning" onclick="testUpdateUser()">2. تحديث مستخدم</button>
            <button class="btn-danger" onclick="testDeleteUser()">3. حذف مستخدم</button>
            <button class="btn-info" onclick="testToggleUserStatus()">4. تغيير حالة المستخدم</button>
            
            <div id="usersResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Interface Test -->
        <div class="test-section">
            <h3><i class="fas fa-desktop"></i> اختبار الواجهة التفاعلية</h3>
            <p>اختبار النماذج والواجهات التفاعلية</p>
            
            <button class="btn-primary" onclick="testLoadInterface()">1. تحميل الواجهة</button>
            <button class="btn-success" onclick="testAddUserModal()">2. اختبار نموذج الإضافة</button>
            <button class="btn-warning" onclick="testEditUserModal()">3. اختبار نموذج التعديل</button>
            <button class="btn-info" onclick="testFiltersAndSearch()">4. اختبار البحث والفلاتر</button>
            
            <div id="interfaceResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Statistics Display -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات المستخدمين</h3>
            <button class="btn-info" onclick="loadUsersStatistics()">تحديث الإحصائيات</button>
            <div id="statsDisplay" class="stats-grid"></div>
        </div>
        
        <!-- Links Section -->
        <div class="test-section">
            <h3><i class="fas fa-link"></i> روابط مفيدة</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="index.html" target="_blank" style="padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-home"></i> لوحة الإدارة الرئيسية
                </a>
                <a href="users-management-standalone.html" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-users"></i> إدارة المستخدمين المستقلة
                </a>
                <a href="setup/users_management_tables.php" target="_blank" style="padding: 10px 20px; background: #ffc107; color: #333; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-database"></i> إعداد جداول المستخدمين
                </a>
                <a href="php/users_management.php?action=get_all" target="_blank" style="padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-code"></i> اختبار API مباشر
                </a>
            </div>
        </div>
    </div>

    <script>
        function showResult(elementId, data, type = 'info') {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        // Database Tests
        async function testDatabaseConnection() {
            try {
                showResult('dbResult', 'جاري اختبار الاتصال بقاعدة البيانات...', 'info');
                
                const response = await fetch('php/users_management.php?action=test_connection');
                const result = await response.json();
                
                if (result.success) {
                    showResult('dbResult', 
                        `✅ تم الاتصال بقاعدة البيانات بنجاح!\n\n` +
                        `📊 معلومات الاتصال:\n${JSON.stringify(result.data, null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('dbResult', '❌ فشل الاتصال: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('dbResult', '❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function testUsersAPI() {
            try {
                showResult('dbResult', 'جاري اختبار API المستخدمين...', 'info');
                
                const response = await fetch('php/users_management.php?action=get_all');
                const result = await response.json();
                
                if (result.success) {
                    const users = result.data.users || [];
                    showResult('dbResult', 
                        `✅ API المستخدمين يعمل بنجاح!\n\n` +
                        `📊 عدد المستخدمين: ${users.length}\n` +
                        `📋 عينة من البيانات:\n${JSON.stringify(users.slice(0, 2), null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('dbResult', '❌ فشل API المستخدمين: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('dbResult', '❌ خطأ في API المستخدمين: ' + error.message, 'error');
            }
        }

        async function testRolesAPI() {
            try {
                showResult('dbResult', 'جاري اختبار API الأدوار...', 'info');
                
                const response = await fetch('php/users_management.php?action=get_roles');
                const result = await response.json();
                
                if (result.success) {
                    const roles = result.data.roles || [];
                    showResult('dbResult', 
                        `✅ API الأدوار يعمل بنجاح!\n\n` +
                        `📊 عدد الأدوار: ${roles.length}\n` +
                        `📋 الأدوار المتاحة:\n${JSON.stringify(roles, null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('dbResult', '❌ فشل API الأدوار: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('dbResult', '❌ خطأ في API الأدوار: ' + error.message, 'error');
            }
        }

        async function testPermissionsAPI() {
            try {
                showResult('dbResult', 'جاري اختبار API الصلاحيات...', 'info');
                
                const response = await fetch('php/users_management.php?action=get_permissions');
                const result = await response.json();
                
                if (result.success) {
                    const permissions = result.data.permissions || [];
                    showResult('dbResult', 
                        `✅ API الصلاحيات يعمل بنجاح!\n\n` +
                        `📊 عدد الصلاحيات: ${permissions.length}\n` +
                        `📋 عينة من الصلاحيات:\n${JSON.stringify(permissions.slice(0, 3), null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('dbResult', '❌ فشل API الصلاحيات: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('dbResult', '❌ خطأ في API الصلاحيات: ' + error.message, 'error');
            }
        }

        // Users Management Tests
        async function testCreateUser() {
            const testUser = {
                first_name: 'مستخدم',
                last_name: 'تجريبي',
                email: 'test_' + Date.now() + '@example.com',
                phone: '0501234567',
                password: 'test123456',
                role_id: 1,
                is_active: 1
            };

            try {
                showResult('usersResult', 'جاري إنشاء مستخدم تجريبي...', 'info');
                
                const response = await fetch('php/users_management.php?action=create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testUser)
                });
                const result = await response.json();
                
                if (result.success) {
                    showResult('usersResult', 
                        `✅ تم إنشاء المستخدم بنجاح!\n\n` +
                        `📝 البيانات المرسلة:\n${JSON.stringify(testUser, null, 2)}\n\n` +
                        `📊 النتيجة:\n${JSON.stringify(result, null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('usersResult', '❌ فشل إنشاء المستخدم: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('usersResult', '❌ خطأ في إنشاء المستخدم: ' + error.message, 'error');
            }
        }

        async function testUpdateUser() {
            // First get a user to update
            try {
                const usersResponse = await fetch('php/users_management.php?action=get_all');
                const usersResult = await usersResponse.json();
                
                if (usersResult.success && usersResult.data.users.length > 0) {
                    const user = usersResult.data.users[0];
                    const updateData = {
                        first_name: user.first_name + ' (محدث)',
                        last_name: user.last_name,
                        email: user.email,
                        phone: user.phone,
                        role_id: user.role_id,
                        is_active: user.is_active
                    };
                    
                    showResult('usersResult', `جاري تحديث المستخدم: ${user.first_name} ${user.last_name}`, 'info');
                    
                    const response = await fetch(`php/users_management.php?action=update&id=${user.id}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(updateData)
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        showResult('usersResult', 
                            `✅ تم تحديث المستخدم بنجاح!\n\n` +
                            `🆔 معرف المستخدم: ${user.id}\n` +
                            `📝 البيانات الجديدة:\n${JSON.stringify(updateData, null, 2)}`, 
                            'success'
                        );
                    } else {
                        showResult('usersResult', '❌ فشل تحديث المستخدم: ' + result.message, 'error');
                    }
                } else {
                    showResult('usersResult', '❌ لا يوجد مستخدمين للتحديث', 'error');
                }
            } catch (error) {
                showResult('usersResult', '❌ خطأ في تحديث المستخدم: ' + error.message, 'error');
            }
        }

        async function testDeleteUser() {
            alert('⚠️ اختبار الحذف معطل لحماية البيانات\n\nيمكنك اختبار الحذف يدوياً من الواجهة الرئيسية');
            showResult('usersResult', 
                `⚠️ اختبار الحذف معطل لحماية البيانات\n\n` +
                `💡 لاختبار وظيفة الحذف:\n` +
                `1. افتح لوحة الإدارة الرئيسية\n` +
                `2. اذهب إلى إدارة المستخدمين\n` +
                `3. انقر على زر الحذف لأي مستخدم تجريبي`, 
                'info'
            );
        }

        async function testToggleUserStatus() {
            try {
                const usersResponse = await fetch('php/users_management.php?action=get_all');
                const usersResult = await usersResponse.json();
                
                if (usersResult.success && usersResult.data.users.length > 0) {
                    const user = usersResult.data.users[0];
                    
                    showResult('usersResult', `جاري تغيير حالة المستخدم: ${user.first_name} ${user.last_name}`, 'info');
                    
                    const response = await fetch(`php/users_management.php?action=toggle_status&id=${user.id}`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        showResult('usersResult', 
                            `✅ تم تغيير حالة المستخدم بنجاح!\n\n` +
                            `🆔 معرف المستخدم: ${user.id}\n` +
                            `📊 الحالة السابقة: ${user.is_active == 1 ? 'نشط' : 'معطل'}\n` +
                            `📊 الحالة الجديدة: ${user.is_active == 1 ? 'معطل' : 'نشط'}`, 
                            'success'
                        );
                    } else {
                        showResult('usersResult', '❌ فشل تغيير حالة المستخدم: ' + result.message, 'error');
                    }
                } else {
                    showResult('usersResult', '❌ لا يوجد مستخدمين لتغيير حالتهم', 'error');
                }
            } catch (error) {
                showResult('usersResult', '❌ خطأ في تغيير حالة المستخدم: ' + error.message, 'error');
            }
        }

        // Interface Tests
        function testLoadInterface() {
            showResult('interfaceResult', 
                `✅ اختبار تحميل الواجهة\n\n` +
                `📋 للاختبار الكامل:\n` +
                `1. افتح لوحة الإدارة الرئيسية\n` +
                `2. انقر على "إعدادات الإدارة"\n` +
                `3. انقر على "إدارة المستخدمين"\n` +
                `4. انقر على "إجبار التحميل الآن"\n\n` +
                `🎯 يجب أن تظهر واجهة تفاعلية مع:\n` +
                `- إحصائيات المستخدمين\n` +
                `- جدول المستخدمين\n` +
                `- أزرار الإضافة والتعديل\n` +
                `- فلاتر البحث`, 
                'success'
            );
        }

        function testAddUserModal() {
            showResult('interfaceResult', 
                `✅ اختبار نموذج إضافة المستخدم\n\n` +
                `📋 للاختبار:\n` +
                `1. افتح إدارة المستخدمين\n` +
                `2. انقر على "إضافة مستخدم جديد"\n` +
                `3. تحقق من ظهور النموذج مع الحقول:\n` +
                `   - الاسم الأول والأخير\n` +
                `   - البريد الإلكتروني\n` +
                `   - رقم الهاتف\n` +
                `   - الدور\n` +
                `   - كلمة المرور\n` +
                `   - حالة النشاط\n\n` +
                `🎯 يجب أن يعمل زر الحفظ والإلغاء`, 
                'success'
            );
        }

        function testEditUserModal() {
            showResult('interfaceResult', 
                `✅ اختبار نموذج تعديل المستخدم\n\n` +
                `📋 للاختبار:\n` +
                `1. افتح إدارة المستخدمين\n` +
                `2. انقر على زر التعديل (✏️) لأي مستخدم\n` +
                `3. تحقق من:\n` +
                `   - تعبئة الحقول بالبيانات الحالية\n` +
                `   - تغيير عنوان النموذج إلى "تعديل المستخدم"\n` +
                `   - عدم ظهور حقول كلمة المرور\n` +
                `   - عمل زر التحديث\n\n` +
                `🎯 يجب حفظ التغييرات في قاعدة البيانات`, 
                'success'
            );
        }

        function testFiltersAndSearch() {
            showResult('interfaceResult', 
                `✅ اختبار البحث والفلاتر\n\n` +
                `📋 للاختبار:\n` +
                `1. افتح إدارة المستخدمين\n` +
                `2. جرب البحث بالاسم أو البريد الإلكتروني\n` +
                `3. جرب فلتر الأدوار\n` +
                `4. جرب فلتر الحالة (نشط/معطل)\n` +
                `5. انقر على زر البحث\n\n` +
                `🎯 يجب أن تتحدث النتائج حسب الفلاتر`, 
                'success'
            );
        }

        async function loadUsersStatistics() {
            try {
                const response = await fetch('php/users_management.php?action=get_all');
                const result = await response.json();
                
                if (result.success) {
                    const users = result.data.users || [];
                    const stats = result.data.stats || {};
                    
                    const activeUsers = users.filter(u => u.is_active == 1).length;
                    const inactiveUsers = users.filter(u => u.is_active == 0).length;
                    
                    const statsHtml = `
                        <div class="stat-card">
                            <h4>إجمالي المستخدمين</h4>
                            <div class="number">${users.length}</div>
                        </div>
                        <div class="stat-card">
                            <h4>المستخدمين النشطين</h4>
                            <div class="number" style="color: #28a745;">${activeUsers}</div>
                        </div>
                        <div class="stat-card">
                            <h4>المستخدمين المعطلين</h4>
                            <div class="number" style="color: #dc3545;">${inactiveUsers}</div>
                        </div>
                        <div class="stat-card">
                            <h4>آخر تحديث</h4>
                            <div style="font-size: 1rem; color: #666;">${new Date().toLocaleString('ar-SA')}</div>
                        </div>
                    `;
                    
                    document.getElementById('statsDisplay').innerHTML = statsHtml;
                } else {
                    document.getElementById('statsDisplay').innerHTML = '<p style="color: #dc3545;">فشل في تحميل الإحصائيات</p>';
                }
            } catch (error) {
                document.getElementById('statsDisplay').innerHTML = '<p style="color: #dc3545;">خطأ في تحميل الإحصائيات: ' + error.message + '</p>';
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 صفحة اختبار إدارة المستخدمين جاهزة');
            loadUsersStatistics();
        });
    </script>
</body>
</html>
