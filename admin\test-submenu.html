<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة الفرعية</title>
    <link rel="stylesheet" href="css/admin.css" />
    <link rel="stylesheet" href="css/admin-settings-menu-enhanced.css" />
    <link rel="stylesheet" href="css/submenu-visibility-fix.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-title {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5rem;
        }
        
        .test-results {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            color: #333;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار القائمة الفرعية</h1>
        
        <!-- Admin Settings Menu Test -->
        <li class="admin-settings-menu expanded">
            <div class="admin-settings-header" onclick="toggleAdminSettings()">
                <div class="admin-settings-header-content">
                    <i class="fas fa-cogs"></i>
                    <span>الإعدادات الإدارية</span>
                </div>
                <i class="fas fa-chevron-down admin-settings-arrow"></i>
            </div>
            <ul class="admin-settings-submenu">
                <li data-section="generalSettings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات العامة</span>
                </li>
                <li data-section="paymentSettings">
                    <i class="fas fa-credit-card"></i>
                    <span>إعدادات الدفع</span>
                </li>
                <li data-section="categories">
                    <i class="fas fa-tags"></i>
                    <span>إدارة الفئات</span>
                </li>
                <li data-section="usersManagement">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </li>
                <li data-section="rolesManagement">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة الأدوار</span>
                </li>
                <li data-section="storeSettings">
                    <i class="fas fa-store"></i>
                    <span>إعدادات المتجر</span>
                </li>
                <li data-section="storesManagement">
                    <i class="fas fa-store-alt"></i>
                    <span>إدارة المتاجر</span>
                </li>
                <li data-section="securitySettings">
                    <i class="fas fa-shield-alt"></i>
                    <span>إعدادات الأمان</span>
                </li>
                <li data-section="subscriptionsManagement">
                    <i class="fas fa-crown"></i>
                    <span>إدارة الاشتراكات</span>
                </li>
            </ul>
        </li>
        
        <div class="test-results" id="testResults">
            <div class="info">🔄 جاري تشغيل الاختبارات...</div>
        </div>
    </div>

    <script>
        function toggleAdminSettings() {
            const menu = document.querySelector('.admin-settings-menu');
            const submenu = document.querySelector('.admin-settings-submenu');
            
            if (menu.classList.contains('expanded')) {
                menu.classList.remove('expanded');
                submenu.style.maxHeight = '0';
            } else {
                menu.classList.add('expanded');
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
            }
        }

        function runTests() {
            const results = document.getElementById('testResults');
            let html = '<h3>📊 نتائج الاختبار:</h3>';
            
            // Test 1: Check if submenu exists
            const submenu = document.querySelector('.admin-settings-submenu');
            if (submenu) {
                html += '<div class="success">✅ القائمة الفرعية موجودة</div>';
            } else {
                html += '<div class="error">❌ القائمة الفرعية غير موجودة</div>';
                results.innerHTML = html;
                return;
            }
            
            // Test 2: Check submenu visibility
            const computedStyle = window.getComputedStyle(submenu);
            html += '<div class="info">📏 خصائص القائمة الفرعية:</div>';
            html += `<div>- Display: ${computedStyle.display}</div>`;
            html += `<div>- Visibility: ${computedStyle.visibility}</div>`;
            html += `<div>- Opacity: ${computedStyle.opacity}</div>`;
            html += `<div>- Max-height: ${computedStyle.maxHeight}</div>`;
            html += `<div>- Overflow: ${computedStyle.overflow}</div>`;
            
            // Test 3: Check submenu items
            const items = submenu.querySelectorAll('li');
            html += `<div class="info">📝 عدد عناصر القائمة: ${items.length}</div>`;
            
            let visibleItems = 0;
            items.forEach((item, index) => {
                const itemStyle = window.getComputedStyle(item);
                const isVisible = itemStyle.display !== 'none' && 
                                 itemStyle.visibility !== 'hidden' && 
                                 itemStyle.opacity !== '0';
                
                if (isVisible) {
                    visibleItems++;
                }
                
                const section = item.getAttribute('data-section');
                const text = item.querySelector('span')?.textContent || 'غير محدد';
                
                if (section === 'securitySettings' || section === 'subscriptionsManagement') {
                    if (isVisible) {
                        html += `<div class="success">✅ ${text} (${section}) - ظاهر</div>`;
                    } else {
                        html += `<div class="error">❌ ${text} (${section}) - مخفي</div>`;
                    }
                }
            });
            
            // Test 4: Overall result
            html += '<div class="info">📊 النتيجة النهائية:</div>';
            if (visibleItems === items.length) {
                html += `<div class="success">🎉 جميع العناصر ظاهرة (${visibleItems}/${items.length})</div>`;
            } else {
                html += `<div class="error">⚠️ بعض العناصر مخفية (${visibleItems}/${items.length})</div>`;
            }
            
            // Test 5: Check specific items
            const securityItem = document.querySelector('[data-section="securitySettings"]');
            const subscriptionItem = document.querySelector('[data-section="subscriptionsManagement"]');
            
            if (securityItem && subscriptionItem) {
                html += '<div class="success">✅ عناصر الأمان والاشتراكات موجودة في DOM</div>';
            } else {
                html += '<div class="error">❌ عناصر الأمان أو الاشتراكات مفقودة من DOM</div>';
            }
            
            results.innerHTML = html;
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 500);
        });
        
        // Force submenu visibility
        function forceVisibility() {
            const submenu = document.querySelector('.admin-settings-submenu');
            if (submenu) {
                submenu.style.display = 'block';
                submenu.style.visibility = 'visible';
                submenu.style.opacity = '1';
                submenu.style.maxHeight = 'none';
                submenu.style.overflow = 'visible';
                
                const items = submenu.querySelectorAll('li');
                items.forEach(item => {
                    item.style.display = 'flex';
                    item.style.visibility = 'visible';
                    item.style.opacity = '1';
                });
            }
        }
        
        forceVisibility();
        setTimeout(forceVisibility, 100);
        setTimeout(forceVisibility, 500);
    </script>
</body>
</html>
