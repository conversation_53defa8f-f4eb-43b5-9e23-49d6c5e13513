<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أقسام الإعدادات</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            padding: 20px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .test-button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .test-content {
            min-height: 400px;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> اختبار أقسام الإعدادات</h1>
            <p>اختبار جميع أقسام الإعدادات في واجهة الإدارة متعددة المستخدمين</p>
        </div>

        <div class="test-buttons">
            <button class="test-button" onclick="testSection('categoriesManagement', 'loadCategoriesContent')">
                <i class="fas fa-tags"></i>
                إدارة الفئات
            </button>
            <button class="test-button" onclick="testSection('paymentSettings', 'loadPaymentSettingsContent')">
                <i class="fas fa-credit-card"></i>
                إعدادات الدفع
            </button>
            <button class="test-button" onclick="testSection('generalSettings', 'loadGeneralSettingsContent')">
                <i class="fas fa-cog"></i>
                الإعدادات العامة
            </button>
            <button class="test-button" onclick="testSection('storeSettings', 'loadStoreSettingsContent')">
                <i class="fas fa-store"></i>
                إعدادات المتجر
            </button>
            <button class="test-button" onclick="testSection('userManagement', 'loadUserManagementContent')">
                <i class="fas fa-users"></i>
                إدارة المستخدمين
            </button>
            <button class="test-button" onclick="testSection('storesManagement', 'loadStoresManagementContent')">
                <i class="fas fa-store-alt"></i>
                إدارة المتاجر
            </button>
            <button class="test-button" onclick="testSection('rolesManagement', 'loadRolesManagementContent')">
                <i class="fas fa-user-shield"></i>
                إدارة الأدوار
            </button>
            <button class="test-button" onclick="testSection('subscriptionsManagement', 'loadSubscriptionsManagementContent')">
                <i class="fas fa-crown"></i>
                إدارة الاشتراكات
            </button>
            <button class="test-button" onclick="testSection('securitySettings', 'loadSecuritySettingsContent')">
                <i class="fas fa-shield-alt"></i>
                إعدادات الأمان
            </button>
            <button class="test-button" onclick="testSection('systemTesting', 'loadSystemTestingContent')">
                <i class="fas fa-vial"></i>
                نظام الاختبار
            </button>
        </div>

        <div id="testStatus" class="status" style="display: none;"></div>

        <div class="test-content">
            <!-- Test containers for each section -->
            <div id="categoriesContent" style="display: none;"></div>
            <div id="categoriesManagementContent" style="display: none;"></div>
            <div id="paymentSettingsContent" style="display: none;"></div>
            <div id="generalSettingsContent" style="display: none;"></div>
            <div id="storeSettingsContent" style="display: none;"></div>
            <div id="userManagementContent" style="display: none;"></div>
            <div id="storesManagementContent" style="display: none;"></div>
            <div id="rolesManagementContent" style="display: none;"></div>
            <div id="subscriptionsManagementContent" style="display: none;"></div>
            <div id="securitySettingsContent" style="display: none;"></div>
            <div id="systemTestingContent" style="display: none;"></div>
            
            <div id="testResult">
                <h3>اختر قسماً لاختباره</h3>
                <p>انقر على أي من الأزرار أعلاه لاختبار القسم المقابل</p>
            </div>
        </div>
    </div>

    <!-- Include admin.js -->
    <script src="js/admin.js"></script>
    
    <script>
        // Test function for settings sections
        async function testSection(sectionId, functionName) {
            const statusDiv = document.getElementById('testStatus');
            const resultDiv = document.getElementById('testResult');
            
            // Hide all containers
            document.querySelectorAll('[id$="Content"]').forEach(el => {
                el.style.display = 'none';
            });
            
            // Show status
            statusDiv.style.display = 'block';
            statusDiv.className = 'status loading';
            statusDiv.innerHTML = `<i class="fas fa-spinner fa-spin"></i> جاري اختبار ${sectionId}...`;
            
            try {
                // Check if function exists
                if (typeof window[functionName] !== 'function') {
                    throw new Error(`الدالة ${functionName} غير موجودة`);
                }
                
                // Show the appropriate container
                const container = document.getElementById(sectionId + 'Content') || 
                                document.getElementById('categoriesContent') ||
                                document.getElementById('categoriesManagementContent');
                
                if (!container) {
                    throw new Error(`الحاوية ${sectionId}Content غير موجودة`);
                }
                
                container.style.display = 'block';
                
                // Call the function
                await window[functionName]();
                
                // Success
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `<i class="fas fa-check"></i> تم تحميل ${sectionId} بنجاح!`;
                
                resultDiv.innerHTML = `
                    <h3><i class="fas fa-check-circle" style="color: #28a745;"></i> نجح الاختبار</h3>
                    <p>تم تحميل قسم ${sectionId} بنجاح وعرض المحتوى المطلوب.</p>
                `;
                
            } catch (error) {
                console.error('Test error:', error);
                
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `<i class="fas fa-times"></i> فشل في تحميل ${sectionId}`;
                
                resultDiv.innerHTML = `
                    <h3><i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i> فشل الاختبار</h3>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                    <p><strong>القسم:</strong> ${sectionId}</p>
                    <p><strong>الدالة:</strong> ${functionName}</p>
                `;
            }
        }
        
        // Initialize notification manager if not available
        if (typeof notificationManager === 'undefined') {
            window.notificationManager = {
                showSuccess: (msg) => console.log('Success:', msg),
                showError: (msg) => console.log('Error:', msg),
                showInfo: (msg) => console.log('Info:', msg)
            };
        }
        
        console.log('Settings sections test page loaded');
    </script>
</body>
</html>
