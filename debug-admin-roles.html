<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص إدارة الأدوار في لوحة التحكم</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        #rolesManagementContent { border: 2px solid #ddd; border-radius: 10px; padding: 20px; margin-top: 20px; min-height: 300px; background: #f8f9fa; }
        .console-log { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; max-height: 200px; overflow-y: auto; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص إدارة الأدوار في لوحة التحكم</h1>
        <p>فحص مشكلة عرض المحتوى الافتراضي بدلاً من واجهة إدارة الأدوار</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="testFunctionAvailability()">
                <i class="fas fa-search"></i> فحص توفر الوظائف
            </button>
            <button class="test-button" onclick="simulateAdminPanelCall()">
                <i class="fas fa-play"></i> محاكاة استدعاء لوحة التحكم
            </button>
            <button class="test-button" onclick="testDirectCall()">
                <i class="fas fa-cog"></i> اختبار الاستدعاء المباشر
            </button>
            <button class="test-button" onclick="checkForPlaceholderContent()">
                <i class="fas fa-search"></i> البحث عن المحتوى الافتراضي
            </button>
            <button class="test-button" onclick="clearAll()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>

        <div id="testResults"></div>

        <div id="consoleOutput">
            <h3>📝 سجل وحدة التحكم:</h3>
            <div id="consoleLog" class="console-log"></div>
        </div>

        <!-- Container for role management content -->
        <div id="rolesManagementContent">
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>منطقة اختبار إدارة الأدوار</h3>
                <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
            </div>
        </div>
    </div>

    <!-- Load the users management script -->
    <script src="admin/js/users-management.js"></script>

    <script>
        // Capture console logs
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = [];
        
        function captureConsole(type, ...args) {
            const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
            consoleOutput.push(`[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`);
            updateConsoleDisplay();
        }
        
        console.log = function(...args) {
            captureConsole('log', ...args);
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            captureConsole('error', ...args);
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            captureConsole('warn', ...args);
            originalConsoleWarn.apply(console, args);
        };
        
        function updateConsoleDisplay() {
            const consoleLog = document.getElementById('consoleLog');
            consoleLog.innerHTML = consoleOutput.slice(-20).join('\n');
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAll() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleLog').innerHTML = '';
            consoleOutput.length = 0;
            document.getElementById('rolesManagementContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3>منطقة اختبار إدارة الأدوار</h3>
                    <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
                </div>
            `;
        }

        function testFunctionAvailability() {
            addResult('<h3>🔍 فحص توفر الوظائف:</h3>');
            
            const functions = [
                'loadRolesManagementContent',
                'showRolesManagement', 
                'loadRolesManagementInterface',
                'loadRolesData',
                'showAddRoleModal',
                'closeRoleModal',
                'saveRole',
                'editRole',
                'deleteRole'
            ];

            let functionsAvailable = 0;
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} - متوفرة`, 'success');
                    functionsAvailable++;
                } else {
                    addResult(`❌ ${funcName} - غير متوفرة`, 'error');
                }
            });
            
            addResult(`📊 الوظائف المتوفرة: ${functionsAvailable}/${functions.length}`, 'info');
        }

        function simulateAdminPanelCall() {
            addResult('<h3>🎭 محاكاة استدعاء لوحة التحكم:</h3>');
            
            // Simulate the exact call that happens in the admin panel
            addResult('🔄 محاكاة النقر على "إدارة الأدوار" في لوحة التحكم...', 'info');
            
            console.log('Loading roles management...');
            if (typeof loadRolesManagementContent === 'function') {
                addResult('✅ الوظيفة loadRolesManagementContent متوفرة', 'success');
                addResult('🔄 استدعاء الوظيفة...', 'info');
                
                try {
                    loadRolesManagementContent();
                    
                    setTimeout(() => {
                        const content = document.getElementById('rolesManagementContent').innerHTML;
                        addResult('📄 المحتوى بعد الاستدعاء:', 'info');
                        addResult(`<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;">${content.substring(0, 500)}...</pre>`, 'info');
                        
                        if (content.includes('إدارة الأدوار والصلاحيات')) {
                            addResult('✅ تم تحميل واجهة إدارة الأدوار بنجاح!', 'success');
                        } else if (content.includes('قيد التطوير')) {
                            addResult('❌ لا يزال يعرض المحتوى الافتراضي "قيد التطوير"', 'error');
                        } else if (content.includes('جاري تحميل')) {
                            addResult('⚠️ المحتوى في حالة تحميل...', 'warning');
                        } else {
                            addResult('⚠️ محتوى غير متوقع', 'warning');
                        }
                    }, 1000);
                } catch (error) {
                    addResult(`❌ خطأ في استدعاء الوظيفة: ${error.message}`, 'error');
                }
            } else {
                console.warn('Roles management function not available');
                addResult('❌ الوظيفة loadRolesManagementContent غير متوفرة', 'error');
            }
        }

        function testDirectCall() {
            addResult('<h3>🎯 اختبار الاستدعاء المباشر:</h3>');
            
            try {
                if (typeof showRolesManagement === 'function') {
                    addResult('✅ الوظيفة showRolesManagement متوفرة', 'success');
                    addResult('🔄 استدعاء مباشر للوظيفة...', 'info');
                    
                    showRolesManagement();
                    
                    setTimeout(() => {
                        const content = document.getElementById('rolesManagementContent').innerHTML;
                        if (content.includes('إدارة الأدوار والصلاحيات')) {
                            addResult('✅ الاستدعاء المباشر نجح!', 'success');
                        } else {
                            addResult('⚠️ الاستدعاء المباشر لم ينتج المحتوى المتوقع', 'warning');
                        }
                    }, 1000);
                } else {
                    addResult('❌ الوظيفة showRolesManagement غير متوفرة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في الاستدعاء المباشر: ${error.message}`, 'error');
            }
        }

        function checkForPlaceholderContent() {
            addResult('<h3>🔍 البحث عن المحتوى الافتراضي:</h3>');
            
            // Check current content
            const currentContent = document.getElementById('rolesManagementContent').innerHTML;
            
            const placeholderIndicators = [
                'قيد التطوير',
                'under development',
                'سيتم إضافة المحتوى قريباً',
                'Content will be added soon',
                'هذا القسم قيد التطوير'
            ];
            
            let foundPlaceholders = [];
            placeholderIndicators.forEach(indicator => {
                if (currentContent.includes(indicator)) {
                    foundPlaceholders.push(indicator);
                }
            });
            
            if (foundPlaceholders.length > 0) {
                addResult(`❌ تم العثور على محتوى افتراضي: ${foundPlaceholders.join(', ')}`, 'error');
            } else {
                addResult('✅ لم يتم العثور على محتوى افتراضي في المحتوى الحالي', 'success');
            }
            
            // Check if the content looks like role management interface
            if (currentContent.includes('إدارة الأدوار والصلاحيات')) {
                addResult('✅ المحتوى يبدو وكأنه واجهة إدارة الأدوار', 'success');
            } else if (currentContent.includes('منطقة اختبار')) {
                addResult('ℹ️ المحتوى الحالي هو منطقة الاختبار', 'info');
            } else {
                addResult('⚠️ المحتوى الحالي غير محدد', 'warning');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 اختبار تلقائي عند تحميل الصفحة:</h3>');
                testFunctionAvailability();
            }, 500);
        });
    </script>
</body>
</html>
