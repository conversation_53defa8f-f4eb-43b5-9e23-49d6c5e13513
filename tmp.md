[{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 3980,
	"startColumn": 5,
	"endLineNumber": 3980,
	"endColumn": 6,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'try' expected.",
	"source": "ts",
	"startLineNumber": 3980,
	"startColumn": 7,
	"endLineNumber": 3980,
	"endColumn": 12,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 3993,
	"startColumn": 1,
	"endLineNumber": 3993,
	"endColumn": 2,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5800,
	"startColumn": 18,
	"endLineNumber": 5800,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5801,
	"startColumn": 17,
	"endLineNumber": 5806,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5807,
	"startColumn": 13,
	"endLineNumber": 5807,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5824,
	"startColumn": 18,
	"endLineNumber": 5824,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5825,
	"startColumn": 17,
	"endLineNumber": 5826,
	"endColumn": 90,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5827,
	"startColumn": 13,
	"endLineNumber": 5827,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5832,
	"startColumn": 18,
	"endLineNumber": 5832,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 5832,
	"startColumn": 23,
	"endLineNumber": 5832,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5833,
	"startColumn": 17,
	"endLineNumber": 5900,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 5862,
	"startColumn": 30,
	"endLineNumber": 5862,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 5882,
	"startColumn": 38,
	"endLineNumber": 5882,
	"endColumn": 43,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 5893,
	"startColumn": 30,
	"endLineNumber": 5893,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1382",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'>'}` or `&gt;`?",
	"source": "ts",
	"startLineNumber": 5893,
	"startColumn": 63,
	"endLineNumber": 5893,
	"endColumn": 64,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 5899,
	"startColumn": 22,
	"endLineNumber": 5899,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1382",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'>'}` or `&gt;`?",
	"source": "ts",
	"startLineNumber": 5899,
	"startColumn": 59,
	"endLineNumber": 5899,
	"endColumn": 60,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5901,
	"startColumn": 13,
	"endLineNumber": 5901,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5922,
	"startColumn": 18,
	"endLineNumber": 5922,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5923,
	"startColumn": 17,
	"endLineNumber": 5928,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5929,
	"startColumn": 13,
	"endLineNumber": 5929,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5946,
	"startColumn": 18,
	"endLineNumber": 5946,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5947,
	"startColumn": 17,
	"endLineNumber": 5948,
	"endColumn": 91,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5949,
	"startColumn": 13,
	"endLineNumber": 5949,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5955,
	"startColumn": 30,
	"endLineNumber": 5955,
	"endColumn": 34,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "8013",
	"severity": 8,
	"message": "Non-null assertions can only be used in TypeScript files.",
	"source": "ts",
	"startLineNumber": 5955,
	"startColumn": 35,
	"endLineNumber": 5955,
	"endColumn": 41,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5955,
	"startColumn": 42,
	"endLineNumber": 5955,
	"endColumn": 48,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1434",
	"severity": 8,
	"message": "Unexpected keyword or identifier.",
	"source": "ts",
	"startLineNumber": 5955,
	"startColumn": 50,
	"endLineNumber": 5955,
	"endColumn": 51,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 5991,
	"startColumn": 18,
	"endLineNumber": 5991,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 5992,
	"startColumn": 17,
	"endLineNumber": 5997,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 5998,
	"startColumn": 13,
	"endLineNumber": 5998,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6015,
	"startColumn": 18,
	"endLineNumber": 6015,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6016,
	"startColumn": 17,
	"endLineNumber": 6017,
	"endColumn": 90,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6018,
	"startColumn": 13,
	"endLineNumber": 6018,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6023,
	"startColumn": 18,
	"endLineNumber": 6023,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6023,
	"startColumn": 23,
	"endLineNumber": 6023,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6024,
	"startColumn": 17,
	"endLineNumber": 6043,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6044,
	"startColumn": 13,
	"endLineNumber": 6044,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6057,
	"startColumn": 18,
	"endLineNumber": 6057,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6058,
	"startColumn": 17,
	"endLineNumber": 6063,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6064,
	"startColumn": 13,
	"endLineNumber": 6064,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6078,
	"startColumn": 18,
	"endLineNumber": 6078,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6079,
	"startColumn": 17,
	"endLineNumber": 6080,
	"endColumn": 84,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6081,
	"startColumn": 13,
	"endLineNumber": 6081,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6095,
	"startColumn": 18,
	"endLineNumber": 6095,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6096,
	"startColumn": 17,
	"endLineNumber": 6101,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6102,
	"startColumn": 13,
	"endLineNumber": 6102,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6116,
	"startColumn": 18,
	"endLineNumber": 6116,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6117,
	"startColumn": 17,
	"endLineNumber": 6122,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6123,
	"startColumn": 13,
	"endLineNumber": 6123,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6129,
	"startColumn": 14,
	"endLineNumber": 6129,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6129,
	"startColumn": 19,
	"endLineNumber": 6129,
	"endColumn": 20,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6130,
	"startColumn": 13,
	"endLineNumber": 6154,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6155,
	"startColumn": 9,
	"endLineNumber": 6155,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6196,
	"startColumn": 40,
	"endLineNumber": 6196,
	"endColumn": 41,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6196,
	"startColumn": 41,
	"endLineNumber": 6196,
	"endColumn": 42,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6243,
	"startColumn": 40,
	"endLineNumber": 6243,
	"endColumn": 41,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6243,
	"startColumn": 41,
	"endLineNumber": 6243,
	"endColumn": 42,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "':' expected.",
	"source": "ts",
	"startLineNumber": 6286,
	"startColumn": 18,
	"endLineNumber": 6286,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6287,
	"startColumn": 17,
	"endLineNumber": 6288,
	"endColumn": 93,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6289,
	"startColumn": 13,
	"endLineNumber": 6289,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6294,
	"startColumn": 18,
	"endLineNumber": 6294,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6294,
	"startColumn": 23,
	"endLineNumber": 6294,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6295,
	"startColumn": 17,
	"endLineNumber": 6314,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6315,
	"startColumn": 13,
	"endLineNumber": 6315,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6328,
	"startColumn": 18,
	"endLineNumber": 6328,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6329,
	"startColumn": 17,
	"endLineNumber": 6334,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6335,
	"startColumn": 13,
	"endLineNumber": 6335,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6349,
	"startColumn": 18,
	"endLineNumber": 6349,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6350,
	"startColumn": 17,
	"endLineNumber": 6351,
	"endColumn": 89,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6352,
	"startColumn": 13,
	"endLineNumber": 6352,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6384,
	"startColumn": 18,
	"endLineNumber": 6384,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6385,
	"startColumn": 17,
	"endLineNumber": 6390,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6391,
	"startColumn": 13,
	"endLineNumber": 6391,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6405,
	"startColumn": 18,
	"endLineNumber": 6405,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6406,
	"startColumn": 17,
	"endLineNumber": 6411,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6412,
	"startColumn": 13,
	"endLineNumber": 6412,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6418,
	"startColumn": 14,
	"endLineNumber": 6418,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6418,
	"startColumn": 19,
	"endLineNumber": 6418,
	"endColumn": 20,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6419,
	"startColumn": 13,
	"endLineNumber": 6463,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6464,
	"startColumn": 9,
	"endLineNumber": 6464,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6505,
	"startColumn": 40,
	"endLineNumber": 6505,
	"endColumn": 41,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6505,
	"startColumn": 41,
	"endLineNumber": 6505,
	"endColumn": 42,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "':' expected.",
	"source": "ts",
	"startLineNumber": 6534,
	"startColumn": 43,
	"endLineNumber": 6534,
	"endColumn": 50,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1101",
	"severity": 8,
	"message": "'with' statements are not allowed in strict mode.",
	"source": "ts",
	"startLineNumber": 6534,
	"startColumn": 51,
	"endLineNumber": 6534,
	"endColumn": 55,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'(' expected.",
	"source": "ts",
	"startLineNumber": 6534,
	"startColumn": 56,
	"endLineNumber": 6534,
	"endColumn": 58,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "')' expected.",
	"source": "ts",
	"startLineNumber": 6534,
	"startColumn": 59,
	"endLineNumber": 6534,
	"endColumn": 66,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6534,
	"startColumn": 67,
	"endLineNumber": 6534,
	"endColumn": 70,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6583,
	"startColumn": 40,
	"endLineNumber": 6583,
	"endColumn": 41,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1003",
	"severity": 8,
	"message": "Identifier expected.",
	"source": "ts",
	"startLineNumber": 6583,
	"startColumn": 41,
	"endLineNumber": 6583,
	"endColumn": 42,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "':' expected.",
	"source": "ts",
	"startLineNumber": 6626,
	"startColumn": 18,
	"endLineNumber": 6626,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6627,
	"startColumn": 17,
	"endLineNumber": 6628,
	"endColumn": 90,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6629,
	"startColumn": 13,
	"endLineNumber": 6629,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6634,
	"startColumn": 18,
	"endLineNumber": 6634,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6634,
	"startColumn": 23,
	"endLineNumber": 6634,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6635,
	"startColumn": 17,
	"endLineNumber": 6658,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6659,
	"startColumn": 13,
	"endLineNumber": 6659,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6666,
	"startColumn": 18,
	"endLineNumber": 6666,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6667,
	"startColumn": 17,
	"endLineNumber": 6672,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6673,
	"startColumn": 13,
	"endLineNumber": 6673,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6688,
	"startColumn": 18,
	"endLineNumber": 6688,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6689,
	"startColumn": 17,
	"endLineNumber": 6690,
	"endColumn": 92,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6691,
	"startColumn": 13,
	"endLineNumber": 6691,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6712,
	"startColumn": 58,
	"endLineNumber": 6712,
	"endColumn": 61,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "',' expected.",
	"source": "ts",
	"startLineNumber": 6712,
	"startColumn": 73,
	"endLineNumber": 6712,
	"endColumn": 74,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "',' expected.",
	"source": "ts",
	"startLineNumber": 6712,
	"startColumn": 82,
	"endLineNumber": 6712,
	"endColumn": 83,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1443",
	"severity": 8,
	"message": "Module declaration names may only use ' or \" quoted strings.",
	"source": "ts",
	"startLineNumber": 6712,
	"startColumn": 91,
	"endLineNumber": 6718,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1434",
	"severity": 8,
	"message": "Unexpected keyword or identifier.",
	"source": "ts",
	"startLineNumber": 6718,
	"startColumn": 31,
	"endLineNumber": 6718,
	"endColumn": 34,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1434",
	"severity": 8,
	"message": "Unexpected keyword or identifier.",
	"source": "ts",
	"startLineNumber": 6718,
	"startColumn": 35,
	"endLineNumber": 6718,
	"endColumn": 37,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1434",
	"severity": 8,
	"message": "Unexpected keyword or identifier.",
	"source": "ts",
	"startLineNumber": 6718,
	"startColumn": 47,
	"endLineNumber": 6718,
	"endColumn": 48,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6729,
	"startColumn": 18,
	"endLineNumber": 6729,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'{' expected.",
	"source": "ts",
	"startLineNumber": 6729,
	"startColumn": 23,
	"endLineNumber": 6729,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6730,
	"startColumn": 17,
	"endLineNumber": 6740,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6741,
	"startColumn": 13,
	"endLineNumber": 6741,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6756,
	"startColumn": 18,
	"endLineNumber": 6756,
	"endColumn": 23,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6757,
	"startColumn": 17,
	"endLineNumber": 6759,
	"endColumn": 40,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6760,
	"startColumn": 13,
	"endLineNumber": 6760,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6778,
	"startColumn": 14,
	"endLineNumber": 6778,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6779,
	"startColumn": 13,
	"endLineNumber": 6820,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 6805,
	"startColumn": 26,
	"endLineNumber": 6805,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 6812,
	"startColumn": 26,
	"endLineNumber": 6812,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6821,
	"startColumn": 9,
	"endLineNumber": 6821,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6836,
	"startColumn": 14,
	"endLineNumber": 6836,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6837,
	"startColumn": 13,
	"endLineNumber": 6884,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6885,
	"startColumn": 9,
	"endLineNumber": 6885,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6900,
	"startColumn": 14,
	"endLineNumber": 6900,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6901,
	"startColumn": 13,
	"endLineNumber": 6942,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 6908,
	"startColumn": 26,
	"endLineNumber": 6908,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6943,
	"startColumn": 9,
	"endLineNumber": 6943,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 6958,
	"startColumn": 14,
	"endLineNumber": 6958,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 6959,
	"startColumn": 13,
	"endLineNumber": 6993,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 6994,
	"startColumn": 9,
	"endLineNumber": 6994,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7009,
	"startColumn": 14,
	"endLineNumber": 7009,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 7010,
	"startColumn": 13,
	"endLineNumber": 7061,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 7062,
	"startColumn": 9,
	"endLineNumber": 7062,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7077,
	"startColumn": 14,
	"endLineNumber": 7077,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 7078,
	"startColumn": 13,
	"endLineNumber": 7144,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7087,
	"startColumn": 30,
	"endLineNumber": 7087,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7093,
	"startColumn": 30,
	"endLineNumber": 7093,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7099,
	"startColumn": 26,
	"endLineNumber": 7099,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7107,
	"startColumn": 26,
	"endLineNumber": 7107,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7111,
	"startColumn": 26,
	"endLineNumber": 7111,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7115,
	"startColumn": 30,
	"endLineNumber": 7115,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7125,
	"startColumn": 30,
	"endLineNumber": 7125,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7131,
	"startColumn": 30,
	"endLineNumber": 7131,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'input' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 7137,
	"startColumn": 26,
	"endLineNumber": 7137,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 7145,
	"startColumn": 9,
	"endLineNumber": 7145,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7160,
	"startColumn": 14,
	"endLineNumber": 7160,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "2657",
	"severity": 8,
	"message": "JSX expressions must have one parent element.",
	"source": "ts",
	"startLineNumber": 7161,
	"startColumn": 13,
	"endLineNumber": 7214,
	"endColumn": 19,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 7215,
	"startColumn": 9,
	"endLineNumber": 7215,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7490,
	"startColumn": 83,
	"endLineNumber": 7490,
	"endColumn": 85,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "']' expected.",
	"source": "ts",
	"startLineNumber": 7490,
	"startColumn": 98,
	"endLineNumber": 7490,
	"endColumn": 99,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7490,
	"startColumn": 113,
	"endLineNumber": 7490,
	"endColumn": 114,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7556,
	"startColumn": 83,
	"endLineNumber": 7556,
	"endColumn": 85,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "']' expected.",
	"source": "ts",
	"startLineNumber": 7556,
	"startColumn": 98,
	"endLineNumber": 7556,
	"endColumn": 99,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 7556,
	"startColumn": 118,
	"endLineNumber": 7556,
	"endColumn": 119,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/js/admin.js",
	"owner": "typescript",
	"code": "1160",
	"severity": 8,
	"message": "Unterminated template literal.",
	"source": "ts",
	"startLineNumber": 7561,
	"startColumn": 5,
	"endLineNumber": 7561,
	"endColumn": 5,
	"extensionID": "vscode.typescript-language-features"
}]


[{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/css/admin-settings-menu-enhanced.css",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": {
		"value": "css-prefix-order",
		"target": {
			"$mid": 1,
			"path": "/docs/user-guide/hints/hint-css-prefix-order/",
			"scheme": "https",
			"authority": "webhint.io"
		}
	},
	"severity": 4,
	"message": "'backdrop-filter' should be listed after '-webkit-backdrop-filter'.",
	"source": "Microsoft Edge Tools",
	"startLineNumber": 14,
	"startColumn": 5,
	"endLineNumber": 14,
	"endColumn": 20,
	"extensionID": "ms-edgedevtools.vscode-edge-devtools"
},{
	"resource": "/k:/Projets_Sites_Web/Mossaab-LandingPage-18-Jul-25/admin/css/admin-settings-menu-enhanced.css",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": {
		"value": "css-prefix-order",
		"target": {
			"$mid": 1,
			"path": "/docs/user-guide/hints/hint-css-prefix-order/",
			"scheme": "https",
			"authority": "webhint.io"
		}
	},
	"severity": 4,
	"message": "'backdrop-filter' should be listed after '-webkit-backdrop-filter'.",
	"source": "Microsoft Edge Tools",
	"startLineNumber": 202,
	"startColumn": 5,
	"endLineNumber": 202,
	"endColumn": 20,
	"extensionID": "ms-edgedevtools.vscode-edge-devtools"
}]