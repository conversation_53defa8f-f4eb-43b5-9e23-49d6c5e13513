/**
 * Reports and Statistics Module
 * Comprehensive reporting system for the admin interface
 */

(function() {
    'use strict';
    
    console.log('📊 Reports and Statistics module loading...');
    
    // Configuration
    const config = {
        currentUserId: 1,
        currentUserRole: 'admin',
        apiBaseUrl: '../php/api/',
        refreshInterval: 60000 // 1 minute
    };
    
    // Load reports and statistics
    function loadReportsAndStatistics() {
        console.log('📊 Loading reports and statistics...');
        
        const reportsContainer = document.getElementById('reportsContent');
        if (!reportsContainer) {
            console.error('Reports container not found');
            return;
        }
        
        // Show loading state
        showReportsLoading();
        
        // Load data from multiple sources
        Promise.all([
            loadSystemOverview(),
            loadSalesReports(),
            loadProductAnalytics(),
            loadUserAnalytics()
        ])
        .then(([systemData, salesData, productData, userData]) => {
            displayReportsAndStatistics({
                system: systemData,
                sales: salesData,
                products: productData,
                users: userData
            });
            console.log('✅ Reports and statistics loaded successfully');
        })
        .catch(error => {
            console.error('❌ Error loading reports:', error);
            showReportsError(error.message);
        });
    }
    
    // Load system overview
    function loadSystemOverview() {
        return fetch(`${config.apiBaseUrl}admin-dashboard.php?action=overview&user_id=${config.currentUserId}&user_role=${config.currentUserRole}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || 'Failed to load system overview');
                }
            });
    }
    
    // Load sales reports
    function loadSalesReports() {
        return fetch(`${config.apiBaseUrl}dashboard-stats.php`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || 'Failed to load sales reports');
                }
            });
    }
    
    // Load product analytics
    function loadProductAnalytics() {
        return fetch(`${config.apiBaseUrl}products-multi-user.php?user_id=${config.currentUserId}&user_role=${config.currentUserRole}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    return {
                        products: data.data,
                        stats: data.stats
                    };
                } else {
                    throw new Error(data.message || 'Failed to load product analytics');
                }
            });
    }
    
    // Load user analytics
    function loadUserAnalytics() {
        return fetch(`${config.apiBaseUrl}admin-dashboard.php?action=users&user_id=${config.currentUserId}&user_role=${config.currentUserRole}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || 'Failed to load user analytics');
                }
            });
    }
    
    // Display reports and statistics
    function displayReportsAndStatistics(data) {
        const reportsContainer = document.getElementById('reportsContent');
        if (!reportsContainer) return;
        
        const isAdmin = config.currentUserRole === 'admin';
        
        let reportsHTML = `
            <div class="reports-dashboard">
                <div class="reports-header">
                    <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
                    <div class="reports-actions">
                        <button class="btn btn-primary" onclick="window.reportsModule.refresh()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                        <button class="btn btn-success" onclick="window.reportsModule.export()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
                
                <!-- System Overview -->
                <div class="reports-section">
                    <h3><i class="fas fa-tachometer-alt"></i> نظرة عامة على النظام</h3>
                    <div class="overview-grid">
                        <div class="overview-card primary">
                            <div class="card-icon"><i class="fas fa-users"></i></div>
                            <div class="card-content">
                                <h4>${data.system.system_stats.total_users}</h4>
                                <p>إجمالي المستخدمين</p>
                                <small>${data.system.system_stats.active_users} نشط</small>
                            </div>
                        </div>
                        
                        <div class="overview-card success">
                            <div class="card-icon"><i class="fas fa-box"></i></div>
                            <div class="card-content">
                                <h4>${data.system.system_stats.total_products}</h4>
                                <p>إجمالي المنتجات</p>
                                <small>${data.system.system_stats.active_products} نشط</small>
                            </div>
                        </div>
                        
                        <div class="overview-card info">
                            <div class="card-icon"><i class="fas fa-bullhorn"></i></div>
                            <div class="card-content">
                                <h4>${data.system.system_stats.total_landing_pages}</h4>
                                <p>صفحات الهبوط</p>
                                <small>${data.system.system_stats.sellers_with_landing_pages} بائع لديه صفحات</small>
                            </div>
                        </div>
                        
                        <div class="overview-card warning">
                            <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                            <div class="card-content">
                                <h4>${data.system.system_stats.total_orders}</h4>
                                <p>إجمالي الطلبات</p>
                                <small>${data.system.system_stats.total_customers} عميل</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sales Analytics -->
                <div class="reports-section">
                    <h3><i class="fas fa-chart-line"></i> تحليل المبيعات</h3>
                    <div class="sales-analytics">
                        <div class="sales-summary">
                            <div class="sales-card">
                                <h4>إجمالي المبيعات</h4>
                                <p class="sales-amount">${data.sales.totalSales} دج</p>
                                <small>جميع الطلبات المكتملة</small>
                            </div>
                            <div class="sales-card">
                                <h4>الطلبات الجديدة</h4>
                                <p class="sales-amount">${data.sales.newOrders}</p>
                                <small>آخر 30 يوم</small>
                            </div>
                            <div class="sales-card">
                                <h4>الطلبات المعلقة</h4>
                                <p class="sales-amount">${data.sales.pendingOrders}</p>
                                <small>تحتاج معالجة</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Product Analytics -->
                <div class="reports-section">
                    <h3><i class="fas fa-cube"></i> تحليل المنتجات</h3>
                    <div class="product-analytics">
                        <div class="product-summary">
                            <div class="product-stat">
                                <span class="stat-label">المنتجات النشطة:</span>
                                <span class="stat-value">${data.products.stats.active_products || data.products.products.filter(p => p.actif == 1).length}</span>
                            </div>
                            <div class="product-stat">
                                <span class="stat-label">منتجات لها صفحات هبوط:</span>
                                <span class="stat-value">${data.products.stats.products_with_landing_pages || data.products.products.filter(p => p.has_landing_page).length}</span>
                            </div>
                            <div class="product-stat">
                                <span class="stat-label">متوسط السعر:</span>
                                <span class="stat-value">${calculateAveragePrice(data.products.products)} دج</span>
                            </div>
                        </div>
                        
                        <div class="top-products">
                            <h4>أفضل المنتجات</h4>
                            <div class="products-list">
        `;
        
        // Display top products
        const topProducts = data.products.products.slice(0, 5);
        topProducts.forEach(product => {
            reportsHTML += `
                <div class="product-item">
                    <div class="product-info">
                        <strong>${product.titre}</strong>
                        <span class="product-price">${product.prix} دج</span>
                    </div>
                    <div class="product-status">
                        ${product.actif == 1 ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>'}
                        ${product.has_landing_page ? '<span class="badge bg-info">له صفحة هبوط</span>' : ''}
                    </div>
                </div>
            `;
        });
        
        reportsHTML += `
                            </div>
                        </div>
                    </div>
                </div>
        `;
        
        // User Analytics (Admin only)
        if (isAdmin && data.users && data.users.length > 0) {
            reportsHTML += `
                <div class="reports-section">
                    <h3><i class="fas fa-users-cog"></i> تحليل المستخدمين</h3>
                    <div class="user-analytics">
                        <div class="users-summary">
                            <div class="user-stat">
                                <span class="stat-label">إجمالي المستخدمين:</span>
                                <span class="stat-value">${data.users.length}</span>
                            </div>
                            <div class="user-stat">
                                <span class="stat-label">البائعين النشطين:</span>
                                <span class="stat-value">${data.users.filter(u => u.role_name === 'seller').length}</span>
                            </div>
                            <div class="user-stat">
                                <span class="stat-label">المديرين:</span>
                                <span class="stat-value">${data.users.filter(u => u.role_name === 'admin').length}</span>
                            </div>
                        </div>
                        
                        <div class="top-users">
                            <h4>أفضل البائعين</h4>
                            <div class="users-list">
            `;
            
            // Display top users
            const topUsers = data.users.filter(u => u.role_name === 'seller').slice(0, 5);
            topUsers.forEach(user => {
                reportsHTML += `
                    <div class="user-item">
                        <div class="user-info">
                            <strong>${user.first_name} ${user.last_name}</strong>
                            <small>@${user.username}</small>
                        </div>
                        <div class="user-stats">
                            <span class="user-stat-item">${user.product_count} منتج</span>
                            <span class="user-stat-item">${user.landing_page_count} صفحة هبوط</span>
                        </div>
                    </div>
                `;
            });
            
            reportsHTML += `
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Recent Activity
        if (data.system.recent_activity && data.system.recent_activity.length > 0) {
            reportsHTML += `
                <div class="reports-section">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <div class="recent-activity">
                        <div class="activity-timeline">
            `;
            
            data.system.recent_activity.slice(0, 10).forEach(activity => {
                const icon = activity.type === 'product' ? 'box' : 
                            activity.type === 'landing_page' ? 'bullhorn' : 'shopping-cart';
                const typeText = activity.type === 'product' ? 'منتج' : 
                               activity.type === 'landing_page' ? 'صفحة هبوط' : 'طلب';
                
                reportsHTML += `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>${activity.title}</strong></p>
                            <small>${typeText} بواسطة ${activity.user} - ${formatDate(activity.date)}</small>
                        </div>
                    </div>
                `;
            });
            
            reportsHTML += `
                        </div>
                    </div>
                </div>
            `;
        }
        
        reportsHTML += `
                <div class="reports-footer">
                    <p class="text-muted">آخر تحديث: ${new Date().toLocaleString('ar-DZ')}</p>
                </div>
            </div>
        `;
        
        reportsContainer.innerHTML = reportsHTML;
    }
    
    // Utility functions
    function calculateAveragePrice(products) {
        if (!products || products.length === 0) return 0;
        const total = products.reduce((sum, product) => sum + parseFloat(product.prix || 0), 0);
        return Math.round(total / products.length);
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function showReportsLoading() {
        const reportsContainer = document.getElementById('reportsContent');
        if (reportsContainer) {
            reportsContainer.innerHTML = `
                <div class="loading-container text-center" style="padding: 60px;">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h3 style="margin-top: 20px;">جاري تحميل التقارير والإحصائيات...</h3>
                    <p>يرجى الانتظار بينما نقوم بجمع البيانات وإعداد التقارير</p>
                </div>
            `;
        }
    }
    
    function showReportsError(message) {
        const reportsContainer = document.getElementById('reportsContent');
        if (reportsContainer) {
            reportsContainer.innerHTML = `
                <div class="error-container text-center" style="padding: 60px;">
                    <div class="alert alert-danger">
                        <h4><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل التقارير</h4>
                        <p>${message}</p>
                        <button class="btn btn-outline-danger" onclick="window.reportsModule.load()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                </div>
            `;
        }
    }
    
    // Export functionality
    function exportReports() {
        console.log('📊 Exporting reports...');
        // This would implement actual export functionality
        alert('ميزة التصدير ستكون متاحة قريباً');
    }
    
    // Public API
    window.reportsModule = {
        load: loadReportsAndStatistics,
        refresh: loadReportsAndStatistics,
        export: exportReports,
        config: config
    };
    
    // Auto-initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ Reports and Statistics module ready');
        
        // Load reports if we're on reports section
        const reportsSection = document.getElementById('reportsContent');
        if (reportsSection) {
            setTimeout(loadReportsAndStatistics, 1000);
        }
    });
    
    console.log('✅ Reports and Statistics module loaded successfully');
    
})();
