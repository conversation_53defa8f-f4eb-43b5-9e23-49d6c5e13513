<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الاشتراكات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        #subscriptionsManagementContent { border: 2px solid #ddd; border-radius: 10px; padding: 20px; margin-top: 20px; min-height: 400px; background: #f8f9fa; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار إدارة الاشتراكات</h1>
        <p>فحص تنفيذ نظام إدارة الاشتراكات في لوحة التحكم</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="testFunctionAvailability()">
                <i class="fas fa-search"></i> فحص توفر الوظائف
            </button>
            <button class="test-button" onclick="testSubscriptionLoad()">
                <i class="fas fa-play"></i> اختبار تحميل الاشتراكات
            </button>
            <button class="test-button" onclick="testAPIConnection()">
                <i class="fas fa-link"></i> اختبار الاتصال بـ API
            </button>
            <button class="test-button" onclick="testDatabaseConnection()">
                <i class="fas fa-database"></i> اختبار قاعدة البيانات
            </button>
            <button class="test-button" onclick="clearAll()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>

        <div id="testResults"></div>

        <!-- Container for subscription management content -->
        <div id="subscriptionsManagementContent">
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-crown" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>منطقة اختبار إدارة الاشتراكات</h3>
                <p>سيتم تحميل محتوى إدارة الاشتراكات هنا</p>
            </div>
        </div>
    </div>

    <!-- Load the subscription management script -->
    <script src="admin/js/subscriptions-management.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAll() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('subscriptionsManagementContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-crown" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3>منطقة اختبار إدارة الاشتراكات</h3>
                    <p>سيتم تحميل محتوى إدارة الاشتراكات هنا</p>
                </div>
            `;
        }

        function testFunctionAvailability() {
            addResult('<h3>🔍 فحص توفر الوظائف:</h3>');
            
            const functions = [
                'loadSubscriptionsManagementContent',
                'showSubscriptionsManagement', 
                'loadSubscriptionsManagementInterface',
                'loadSubscriptionData',
                'displaySubscriptionPlans',
                'showAddSubscriptionModal',
                'editSubscriptionPlan',
                'deleteSubscriptionPlan',
                'updateSubscriptionStats'
            ];

            let functionsAvailable = 0;
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} - متوفرة`, 'success');
                    functionsAvailable++;
                } else {
                    addResult(`❌ ${funcName} - غير متوفرة`, 'error');
                }
            });
            
            addResult(`📊 الوظائف المتوفرة: ${functionsAvailable}/${functions.length}`, 'info');
        }

        function testSubscriptionLoad() {
            addResult('<h3>🎭 اختبار تحميل إدارة الاشتراكات:</h3>');
            
            if (typeof loadSubscriptionsManagementContent === 'function') {
                addResult('✅ الوظيفة loadSubscriptionsManagementContent متوفرة', 'success');
                addResult('🔄 استدعاء الوظيفة...', 'info');
                
                try {
                    loadSubscriptionsManagementContent();
                    
                    setTimeout(() => {
                        const content = document.getElementById('subscriptionsManagementContent').innerHTML;
                        addResult('📄 المحتوى بعد الاستدعاء:', 'info');
                        
                        if (content.includes('إدارة الاشتراكات')) {
                            addResult('✅ تم تحميل واجهة إدارة الاشتراكات بنجاح!', 'success');
                        } else if (content.includes('جاري تحميل')) {
                            addResult('⚠️ المحتوى في حالة تحميل...', 'warning');
                        } else {
                            addResult('⚠️ محتوى غير متوقع', 'warning');
                        }
                        
                        // Check for specific elements
                        if (content.includes('إجمالي الاشتراكات')) {
                            addResult('✅ لوحة معلومات الاشتراكات موجودة', 'success');
                        }
                        
                        if (content.includes('خطط الاشتراك')) {
                            addResult('✅ قسم خطط الاشتراك موجود', 'success');
                        }
                        
                        if (content.includes('إضافة خطة جديدة')) {
                            addResult('✅ زر إضافة خطة جديدة موجود', 'success');
                        }
                        
                    }, 1000);
                } catch (error) {
                    addResult(`❌ خطأ في استدعاء الوظيفة: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ الوظيفة loadSubscriptionsManagementContent غير متوفرة', 'error');
            }
        }

        function testAPIConnection() {
            addResult('<h3>🔗 اختبار الاتصال بـ API:</h3>');
            
            // Test plans endpoint
            fetch('php/api/subscriptions.php?action=plans')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ API خطط الاشتراك متاح', 'success');
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(data => {
                    if (data.success) {
                        addResult('✅ API يعيد بيانات صحيحة للخطط', 'success');
                        addResult(`📊 عدد الخطط: ${data.data ? data.data.length : 0}`, 'info');
                    } else {
                        addResult('⚠️ API يعيد خطأ: ' + (data.message || 'غير محدد'), 'warning');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في الاتصال بـ API الخطط: ${error.message}`, 'error');
                });
            
            // Test stats endpoint
            setTimeout(() => {
                fetch('php/api/subscriptions.php?action=stats')
                    .then(response => {
                        if (response.ok) {
                            addResult('✅ API إحصائيات الاشتراك متاح', 'success');
                            return response.json();
                        } else {
                            throw new Error(`HTTP ${response.status}`);
                        }
                    })
                    .then(data => {
                        if (data.success) {
                            addResult('✅ API يعيد إحصائيات صحيحة', 'success');
                            addResult(`📊 الإحصائيات: ${JSON.stringify(data.data).substring(0, 100)}...`, 'info');
                        } else {
                            addResult('⚠️ API الإحصائيات يعيد خطأ: ' + (data.message || 'غير محدد'), 'warning');
                        }
                    })
                    .catch(error => {
                        addResult(`❌ خطأ في الاتصال بـ API الإحصائيات: ${error.message}`, 'error');
                    });
            }, 500);
        }

        function testDatabaseConnection() {
            addResult('<h3>🗄️ اختبار قاعدة البيانات:</h3>');
            
            // Test database connection through API
            fetch('php/api/subscriptions.php?action=plans')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addResult('✅ الاتصال بقاعدة البيانات يعمل', 'success');
                        addResult('✅ جدول subscription_plans موجود ويمكن الوصول إليه', 'success');
                        
                        if (data.data && data.data.length > 0) {
                            addResult(`📊 يوجد ${data.data.length} خطة اشتراك في قاعدة البيانات`, 'info');
                            
                            // Show first plan details
                            const firstPlan = data.data[0];
                            addResult(`📋 مثال على خطة: ${firstPlan.display_name_ar} - ${firstPlan.price} ${firstPlan.currency}`, 'info');
                        } else {
                            addResult('ℹ️ لا توجد خطط اشتراك في قاعدة البيانات', 'info');
                        }
                    } else {
                        addResult('❌ خطأ في قاعدة البيانات: ' + (data.message || 'غير محدد'), 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في الاتصال بقاعدة البيانات: ${error.message}`, 'error');
                });
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 اختبار تلقائي عند تحميل الصفحة:</h3>');
                testFunctionAvailability();
                setTimeout(() => testAPIConnection(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
