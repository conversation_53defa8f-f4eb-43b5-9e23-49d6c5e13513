<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الفئات</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-family: inherit;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .quick-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-tags"></i>
                اختبار نظام إدارة الفئات
            </h1>
            <p>فحص شامل لجميع مكونات نظام إدارة الفئات</p>
        </div>

        <div class="test-grid">
            <!-- Database Test -->
            <div class="test-section">
                <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
                <p>فحص إنشاء جدول الفئات والبيانات الافتراضية</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="testDatabase()">
                        <i class="fas fa-play"></i>
                        اختبار قاعدة البيانات
                    </button>
                </div>

                <div class="loading" id="dbLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص قاعدة البيانات...</p>
                </div>

                <div class="results" id="dbResults" style="display: none;">
                    <h4>نتائج فحص قاعدة البيانات</h4>
                    <div id="dbResultsList"></div>
                </div>
            </div>

            <!-- API Test -->
            <div class="test-section">
                <h3><i class="fas fa-plug"></i> اختبار API</h3>
                <p>فحص جميع نقاط API لإدارة الفئات</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-success" onclick="testAPI()">
                        <i class="fas fa-code"></i>
                        اختبار API
                    </button>
                </div>

                <div class="loading" id="apiLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص API...</p>
                </div>

                <div class="results" id="apiResults" style="display: none;">
                    <h4>نتائج فحص API</h4>
                    <div id="apiResultsList"></div>
                </div>
            </div>

            <!-- Frontend Test -->
            <div class="test-section">
                <h3><i class="fas fa-desktop"></i> اختبار الواجهة</h3>
                <p>فحص ملفات JavaScript و CSS</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-warning" onclick="testFrontend()">
                        <i class="fas fa-paint-brush"></i>
                        اختبار الواجهة
                    </button>
                </div>

                <div class="loading" id="frontendLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص الواجهة...</p>
                </div>

                <div class="results" id="frontendResults" style="display: none;">
                    <h4>نتائج فحص الواجهة</h4>
                    <div id="frontendResultsList"></div>
                </div>
            </div>

            <!-- Integration Test -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> اختبار التكامل</h3>
                <p>فحص التكامل بين جميع المكونات</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="testIntegration()">
                        <i class="fas fa-link"></i>
                        اختبار التكامل
                    </button>
                </div>

                <div class="loading" id="integrationLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص التكامل...</p>
                </div>

                <div class="results" id="integrationResults" style="display: none;">
                    <h4>نتائج فحص التكامل</h4>
                    <div id="integrationResultsList"></div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <a href="database/setup-test.html" class="btn btn-primary" target="_blank">
                <i class="fas fa-database"></i>
                إعداد قاعدة البيانات
            </a>
            <a href="admin/system-settings/categories.html" class="btn btn-success" target="_blank">
                <i class="fas fa-tags"></i>
                إدارة الفئات
            </a>
            <a href="admin/system-settings/index.html" class="btn btn-warning" target="_blank">
                <i class="fas fa-cogs"></i>
                إعدادات النظام
            </a>
        </div>
    </div>

    <script>
        // Test Database
        async function testDatabase() {
            const loading = document.getElementById('dbLoading');
            const results = document.getElementById('dbResults');
            const resultsList = document.getElementById('dbResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            try {
                const response = await fetch('database/setup-system-settings.php');
                const data = await response.json();
                
                loading.classList.remove('show');
                results.style.display = 'block';
                
                resultsList.innerHTML = '';
                
                if (data.verification) {
                    data.verification.forEach(table => {
                        const item = document.createElement('div');
                        item.className = 'result-item';
                        item.innerHTML = `
                            <span><strong>${table.name_ar}</strong> - ${table.count} صف</span>
                            <span class="status ${table.status}">${table.exists ? 'موجود' : 'غير موجود'}</span>
                        `;
                        resultsList.appendChild(item);
                    });
                }
                
            } catch (error) {
                loading.classList.remove('show');
                showNotification('فشل في اختبار قاعدة البيانات: ' + error.message, 'error');
            }
        }

        // Test API
        async function testAPI() {
            const loading = document.getElementById('apiLoading');
            const results = document.getElementById('apiResults');
            const resultsList = document.getElementById('apiResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            const endpoints = [
                { name: 'جلب الفئات', url: 'api/system/categories.php', method: 'GET' },
                { name: 'شجرة الفئات', url: 'api/system/categories.php?action=tree', method: 'GET' },
                { name: 'إحصائيات الفئات', url: 'api/system/categories.php?action=stats', method: 'GET' }
            ];
            
            resultsList.innerHTML = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    const data = await response.json();
                    
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status ${data.success ? 'success' : 'error'}">
                            ${data.success ? 'يعمل' : 'خطأ'}
                        </span>
                    `;
                    resultsList.appendChild(item);
                    
                } catch (error) {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status error">فشل</span>
                    `;
                    resultsList.appendChild(item);
                }
            }
            
            loading.classList.remove('show');
            results.style.display = 'block';
        }

        // Test Frontend
        async function testFrontend() {
            const loading = document.getElementById('frontendLoading');
            const results = document.getElementById('frontendResults');
            const resultsList = document.getElementById('frontendResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            const files = [
                { name: 'CSS الأساسي', url: 'admin/system-settings/css/system-settings.css' },
                { name: 'JavaScript الأساسي', url: 'admin/system-settings/js/core.js' },
                { name: 'JavaScript الفئات', url: 'admin/system-settings/js/categories.js' },
                { name: 'صفحة الفئات', url: 'admin/system-settings/categories.html' }
            ];
            
            resultsList.innerHTML = '';
            
            for (const file of files) {
                try {
                    const response = await fetch(file.url);
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? 'موجود' : 'غير موجود';
                    
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${file.name}</strong></span>
                        <span class="status ${status}">${statusText}</span>
                    `;
                    resultsList.appendChild(item);
                    
                } catch (error) {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${file.name}</strong></span>
                        <span class="status error">خطأ</span>
                    `;
                    resultsList.appendChild(item);
                }
            }
            
            loading.classList.remove('show');
            results.style.display = 'block';
        }

        // Test Integration
        async function testIntegration() {
            const loading = document.getElementById('integrationLoading');
            const results = document.getElementById('integrationResults');
            const resultsList = document.getElementById('integrationResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            const tests = [
                { name: 'تحميل SystemSettings', test: () => typeof SystemSettings !== 'undefined' },
                { name: 'تحميل CategoriesManager', test: () => typeof CategoriesManager !== 'undefined' },
                { name: 'اتصال قاعدة البيانات', test: async () => {
                    try {
                        const response = await fetch('api/system/categories.php?action=stats');
                        const data = await response.json();
                        return data.success;
                    } catch {
                        return false;
                    }
                }}
            ];
            
            resultsList.innerHTML = '';
            
            for (const test of tests) {
                try {
                    let result;
                    if (typeof test.test === 'function') {
                        result = await test.test();
                    } else {
                        result = test.test;
                    }
                    
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${test.name}</strong></span>
                        <span class="status ${result ? 'success' : 'error'}">
                            ${result ? 'نجح' : 'فشل'}
                        </span>
                    `;
                    resultsList.appendChild(item);
                    
                } catch (error) {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${test.name}</strong></span>
                        <span class="status error">خطأ</span>
                    `;
                    resultsList.appendChild(item);
                }
            }
            
            loading.classList.remove('show');
            results.style.display = 'block';
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
