<?php
/**
 * Role Management Database Schema Creator
 * منشئ مخطط قاعدة بيانات إدارة الأدوار
 * 
 * This script creates the complete role management system tables
 */

session_start();
require_once '../config/config.php';

// Set execution time limit
set_time_limit(300);

// HTML header
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء نظام إدارة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; border-radius: 4px; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; font-size: 12px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ إنشاء نظام إدارة الأدوار</h1>
            <p>إنشاء جداول الأدوار والصلاحيات المطلوبة للنظام</p>
        </div>

<?php

class RoleManagementSchemaCreator {
    private $pdo;
    private $results = [];
    
    public function __construct() {
        try {
            $dbConfig = Config::getDbConfig();
            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
            $this->log('success', 'تم الاتصال بقاعدة البيانات بنجاح');
        } catch (Exception $e) {
            $this->log('error', 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
            throw $e;
        }
    }
    
    private function log($type, $message, $details = null) {
        $this->results[] = [
            'type' => $type,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Output immediately for real-time feedback
        $class = $type;
        echo "<div class='step $class'>";
        echo "<strong>" . date('H:i:s') . "</strong> - $message";
        if ($details) {
            echo "<div class='code'>$details</div>";
        }
        echo "</div>";
        flush();
    }
    
    public function createPermissionsTable() {
        $this->log('info', '🔐 إنشاء جدول الصلاحيات (permissions)...');
        
        try {
            $createPermissionsSQL = "
            CREATE TABLE IF NOT EXISTS permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                display_name_ar VARCHAR(150) NOT NULL,
                display_name_en VARCHAR(150) NOT NULL,
                description_ar TEXT,
                description_en TEXT,
                category VARCHAR(50) NOT NULL DEFAULT 'general',
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_name (name),
                INDEX idx_category (category),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->pdo->exec($createPermissionsSQL);
            $this->log('success', '✅ تم إنشاء جدول permissions بنجاح');
            
            return true;
        } catch (Exception $e) {
            $this->log('error', 'خطأ في إنشاء جدول permissions: ' . $e->getMessage());
            throw $e;
        }
    }
    
    public function createUserRolesTable() {
        $this->log('info', '👥 إنشاء جدول الأدوار (user_roles)...');
        
        try {
            $createUserRolesSQL = "
            CREATE TABLE IF NOT EXISTS user_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                display_name_ar VARCHAR(100) NOT NULL,
                display_name_en VARCHAR(100) NOT NULL,
                description_ar TEXT,
                description_en TEXT,
                level INT NOT NULL DEFAULT 1,
                color VARCHAR(7) DEFAULT '#007bff',
                icon VARCHAR(50) DEFAULT 'fas fa-user',
                is_active TINYINT(1) DEFAULT 1,
                is_default TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_name (name),
                INDEX idx_level (level),
                INDEX idx_is_active (is_active),
                INDEX idx_is_default (is_default)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->pdo->exec($createUserRolesSQL);
            $this->log('success', '✅ تم إنشاء جدول user_roles بنجاح');
            
            return true;
        } catch (Exception $e) {
            $this->log('error', 'خطأ في إنشاء جدول user_roles: ' . $e->getMessage());
            throw $e;
        }
    }
    
    public function createRolePermissionsTable() {
        $this->log('info', '🔗 إنشاء جدول ربط الأدوار بالصلاحيات (role_permissions)...');
        
        try {
            $createRolePermissionsSQL = "
            CREATE TABLE IF NOT EXISTS role_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                role_id INT NOT NULL,
                permission_id INT NOT NULL,
                granted TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                UNIQUE KEY unique_role_permission (role_id, permission_id),
                INDEX idx_role_id (role_id),
                INDEX idx_permission_id (permission_id),
                INDEX idx_granted (granted),
                
                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->pdo->exec($createRolePermissionsSQL);
            $this->log('success', '✅ تم إنشاء جدول role_permissions بنجاح');
            
            return true;
        } catch (Exception $e) {
            $this->log('error', 'خطأ في إنشاء جدول role_permissions: ' . $e->getMessage());
            throw $e;
        }
    }
    
    public function createUserRoleAssignmentsTable() {
        $this->log('info', '👤 إنشاء جدول تعيين الأدوار للمستخدمين (user_role_assignments)...');
        
        try {
            $createUserRoleAssignmentsSQL = "
            CREATE TABLE IF NOT EXISTS user_role_assignments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                role_id INT NOT NULL,
                assigned_by INT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                is_active TINYINT(1) DEFAULT 1,
                
                UNIQUE KEY unique_user_role (user_id, role_id),
                INDEX idx_user_id (user_id),
                INDEX idx_role_id (role_id),
                INDEX idx_assigned_by (assigned_by),
                INDEX idx_is_active (is_active),
                INDEX idx_expires_at (expires_at),
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->pdo->exec($createUserRoleAssignmentsSQL);
            $this->log('success', '✅ تم إنشاء جدول user_role_assignments بنجاح');
            
            return true;
        } catch (Exception $e) {
            $this->log('error', 'خطأ في إنشاء جدول user_role_assignments: ' . $e->getMessage());
            throw $e;
        }
    }
    
    public function updateUsersTable() {
        $this->log('info', '🔄 تحديث جدول المستخدمين لدعم الأدوار...');
        
        try {
            // Check if role_id column exists
            $stmt = $this->pdo->query("SHOW COLUMNS FROM users LIKE 'role_id'");
            if ($stmt->rowCount() == 0) {
                // Add role_id column
                $this->pdo->exec("ALTER TABLE users ADD COLUMN role_id INT DEFAULT 4 AFTER phone");
                $this->log('success', '✅ تم إضافة عمود role_id إلى جدول users');
            } else {
                $this->log('info', 'ℹ️ عمود role_id موجود بالفعل في جدول users');
            }
            
            // Add foreign key constraint if it doesn't exist
            try {
                $this->pdo->exec("ALTER TABLE users ADD CONSTRAINT fk_users_role_id FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL");
                $this->log('success', '✅ تم إضافة قيد المفتاح الخارجي لـ role_id');
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    $this->log('info', 'ℹ️ قيد المفتاح الخارجي موجود بالفعل');
                } else {
                    $this->log('warning', '⚠️ تعذر إضافة قيد المفتاح الخارجي: ' . $e->getMessage());
                }
            }
            
            return true;
        } catch (Exception $e) {
            $this->log('error', 'خطأ في تحديث جدول users: ' . $e->getMessage());
            throw $e;
        }
    }
    
    public function createCompleteSchema() {
        $this->log('info', '🚀 بدء إنشاء مخطط إدارة الأدوار الكامل...');
        
        try {
            // Create tables in the correct order (respecting foreign key dependencies)
            $this->createPermissionsTable();
            $this->createUserRolesTable();
            $this->createRolePermissionsTable();
            $this->createUserRoleAssignmentsTable();
            $this->updateUsersTable();
            
            $this->log('success', '🎉 تم إنشاء مخطط إدارة الأدوار بنجاح!');
            
            return true;
        } catch (Exception $e) {
            $this->log('error', '❌ فشل في إنشاء مخطط إدارة الأدوار: ' . $e->getMessage());
            return false;
        }
    }
}

// Execute schema creation if requested
if (isset($_GET['action']) && $_GET['action'] === 'create') {
    try {
        $creator = new RoleManagementSchemaCreator();
        $success = $creator->createCompleteSchema();
        
        if ($success) {
            echo "<div class='step success'>";
            echo "<h3>✅ تم إنشاء مخطط إدارة الأدوار بنجاح!</h3>";
            echo "<p>يمكنك الآن المتابعة لإضافة الأدوار والصلاحيات الافتراضية.</p>";
            echo "<a href='populate-default-roles.php' class='btn btn-primary'>إضافة البيانات الافتراضية</a>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='step error'>";
        echo "<h3>❌ فشل في إنشاء المخطط</h3>";
        echo "<p>خطأ: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
} else {
    // Show schema creation interface
    ?>
    <div class="step info">
        <h3>📋 معلومات مخطط إدارة الأدوار</h3>
        <p>سيتم إنشاء الجداول التالية:</p>
        <ul>
            <li><strong>permissions</strong> - جدول الصلاحيات</li>
            <li><strong>user_roles</strong> - جدول الأدوار</li>
            <li><strong>role_permissions</strong> - جدول ربط الأدوار بالصلاحيات</li>
            <li><strong>user_role_assignments</strong> - جدول تعيين الأدوار للمستخدمين</li>
        </ul>
        
        <h4>التحديثات على الجداول الموجودة:</h4>
        <ul>
            <li>إضافة عمود <code>role_id</code> إلى جدول <code>users</code></li>
            <li>إضافة قيود المفاتيح الخارجية</li>
        </ul>
    </div>
    
    <div class="step warning">
        <h3>⚠️ تحذير مهم</h3>
        <p>تأكد من إنشاء نسخة احتياطية من قاعدة البيانات قبل المتابعة!</p>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="?action=create" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من المتابعة؟ تأكد من إنشاء نسخة احتياطية أولاً!')">
            🚀 إنشاء مخطط إدارة الأدوار
        </a>
        <a href="index.html" class="btn btn-danger">
            ↩️ العودة للوحة الإدارة
        </a>
    </div>
    <?php
}
?>

    </div>
</body>
</html>
