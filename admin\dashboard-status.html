<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة لوحة التحكم - Dashboard Status</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .card-icon {
            font-size: 2rem;
            margin-left: 15px;
            color: #667eea;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #555;
        }
        
        .status-value {
            font-weight: bold;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .log-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tachometer-alt"></i> حالة لوحة التحكم</h1>
            <p>فحص شامل لحالة النظام والمكونات</p>
        </div>
        
        <div class="status-grid">
            <!-- Database Status -->
            <div class="status-card">
                <div class="card-header">
                    <i class="fas fa-database card-icon"></i>
                    <h3 class="card-title">حالة قاعدة البيانات</h3>
                </div>
                <div id="databaseStatus" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري فحص قاعدة البيانات...</p>
                </div>
            </div>
            
            <!-- JavaScript Functions -->
            <div class="status-card">
                <div class="card-header">
                    <i class="fas fa-code card-icon"></i>
                    <h3 class="card-title">وظائف JavaScript</h3>
                </div>
                <div id="jsStatus" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري فحص الوظائف...</p>
                </div>
            </div>
            
            <!-- API Endpoints -->
            <div class="status-card">
                <div class="card-header">
                    <i class="fas fa-plug card-icon"></i>
                    <h3 class="card-title">نقاط API</h3>
                </div>
                <div id="apiStatus" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري فحص API...</p>
                </div>
            </div>
            
            <!-- File System -->
            <div class="status-card">
                <div class="card-header">
                    <i class="fas fa-folder card-icon"></i>
                    <h3 class="card-title">نظام الملفات</h3>
                </div>
                <div id="fileStatus" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري فحص الملفات...</p>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="checkAllStatus()">
                <i class="fas fa-sync-alt"></i> إعادة فحص
            </button>
            <button class="btn btn-success" onclick="fixDatabase()">
                <i class="fas fa-wrench"></i> إصلاح قاعدة البيانات
            </button>
            <button class="btn btn-warning" onclick="openDashboard()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
        </div>
        
        <div class="log-container" id="logContainer" style="display: none;">
            <h4><i class="fas fa-list"></i> سجل العمليات</h4>
            <div id="logContent"></div>
        </div>
    </div>
    
    <script>
        // Initialize status check on page load
        document.addEventListener('DOMContentLoaded', () => {
            checkAllStatus();
        });
        
        // Check all system status
        async function checkAllStatus() {
            await Promise.all([
                checkDatabaseStatus(),
                checkJavaScriptFunctions(),
                checkAPIEndpoints(),
                checkFileSystem()
            ]);
        }
        
        // Check database status
        async function checkDatabaseStatus() {
            const container = document.getElementById('databaseStatus');
            
            try {
                const response = await fetch('../php/api/fix-roles.php?action=check_status');
                const result = await response.json();
                
                if (result.success) {
                    const status = result.status;
                    let html = '';
                    
                    // Check tables
                    Object.entries(status.tables || {}).forEach(([table, exists]) => {
                        html += `
                            <div class="status-item">
                                <span class="status-label">جدول ${table}</span>
                                <span class="status-value ${exists ? 'status-success' : 'status-error'}">
                                    ${exists ? '✅ موجود' : '❌ مفقود'}
                                </span>
                            </div>
                        `;
                    });
                    
                    // Check granted column
                    if (status.has_granted_column !== undefined) {
                        html += `
                            <div class="status-item">
                                <span class="status-label">عمود granted</span>
                                <span class="status-value ${status.has_granted_column ? 'status-success' : 'status-error'}">
                                    ${status.has_granted_column ? '✅ موجود' : '❌ مفقود'}
                                </span>
                            </div>
                        `;
                    }
                    
                    container.innerHTML = html;
                } else {
                    container.innerHTML = `
                        <div class="status-item">
                            <span class="status-label">حالة قاعدة البيانات</span>
                            <span class="status-value status-error">❌ خطأ في الاتصال</span>
                        </div>
                    `;
                }
            } catch (error) {
                container.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">حالة قاعدة البيانات</span>
                        <span class="status-value status-error">❌ فشل الاتصال</span>
                    </div>
                `;
            }
        }
        
        // Check JavaScript functions
        function checkJavaScriptFunctions() {
            const container = document.getElementById('jsStatus');
            const functions = [
                'loadStoresManagementContent',
                'loadReportsContent',
                'initializeSecuritySettings',
                'updateSecurityScore'
            ];
            
            let html = '';
            functions.forEach(func => {
                const exists = typeof window[func] === 'function';
                html += `
                    <div class="status-item">
                        <span class="status-label">${func}</span>
                        <span class="status-value ${exists ? 'status-success' : 'status-error'}">
                            ${exists ? '✅ متوفر' : '❌ مفقود'}
                        </span>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Check API endpoints
        async function checkAPIEndpoints() {
            const container = document.getElementById('apiStatus');
            const endpoints = [
                { name: 'Security Settings', url: '../php/api/security-settings.php?action=get_stats' },
                { name: 'Demo Data', url: '../php/api/demo-data.php?action=get_stats' },
                { name: 'Fix Roles', url: '../php/api/fix-roles.php?action=check_status' }
            ];
            
            let html = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    const isOk = response.ok;
                    
                    html += `
                        <div class="status-item">
                            <span class="status-label">${endpoint.name}</span>
                            <span class="status-value ${isOk ? 'status-success' : 'status-error'}">
                                ${isOk ? '✅ يعمل' : '❌ لا يعمل'}
                            </span>
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="status-item">
                            <span class="status-label">${endpoint.name}</span>
                            <span class="status-value status-error">❌ خطأ</span>
                        </div>
                    `;
                }
            }
            
            container.innerHTML = html;
        }
        
        // Check file system
        function checkFileSystem() {
            const container = document.getElementById('fileStatus');
            const files = [
                'index.html',
                'js/security-settings.js',
                'js/stores-management.js',
                'js/reports.js'
            ];
            
            let html = '';
            files.forEach(file => {
                // Simple check - we'll assume files exist if we got this far
                html += `
                    <div class="status-item">
                        <span class="status-label">${file}</span>
                        <span class="status-value status-success">✅ موجود</span>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Fix database
        async function fixDatabase() {
            const logContainer = document.getElementById('logContainer');
            const logContent = document.getElementById('logContent');
            
            logContainer.style.display = 'block';
            logContent.innerHTML = '<div class="log-item log-info">🔧 بدء إصلاح قاعدة البيانات...</div>';
            
            try {
                const response = await fetch('../php/api/fix-roles.php?action=fix_all', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    logContent.innerHTML += '<div class="log-item log-success">✅ تم إصلاح قاعدة البيانات بنجاح</div>';
                    
                    if (result.results) {
                        result.results.forEach(msg => {
                            logContent.innerHTML += `<div class="log-item log-info">ℹ️ ${msg}</div>`;
                        });
                    }
                    
                    // Refresh status
                    setTimeout(() => {
                        checkAllStatus();
                    }, 2000);
                } else {
                    logContent.innerHTML += `<div class="log-item log-error">❌ فشل الإصلاح: ${result.message}</div>`;
                }
            } catch (error) {
                logContent.innerHTML += `<div class="log-item log-error">❌ خطأ في الإصلاح: ${error.message}</div>`;
            }
        }
        
        // Open dashboard
        function openDashboard() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
