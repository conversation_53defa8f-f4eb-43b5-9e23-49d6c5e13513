<?php

/**
 * <PERSON><PERSON>t to create missing database tables
 */

require_once 'config/config.php';

try {
    $pdo = getPDOConnection();
    echo "Database connection established.\n";
    
    // Create role_permissions table if it doesn't exist
    $sql = "
    CREATE TABLE IF NOT EXISTS role_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        role_id INT NOT NULL,
        permission_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_role_permission (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "role_permissions table created successfully.\n";
    
    // Create permissions table if it doesn't exist
    $sql = "
    CREATE TABLE IF NOT EXISTS permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "permissions table created successfully.\n";
    
    // Create roles table if it doesn't exist
    $sql = "
    CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "roles table created successfully.\n";
    
    // Create user_roles table if it doesn't exist
    $sql = "
    CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_role (user_id, role_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "user_roles table created successfully.\n";
    
    // Insert default roles if they don't exist
    $defaultRoles = [
        ['admin', 'Administrator with full access'],
        ['user', 'Regular user with limited access'],
        ['moderator', 'Moderator with content management access']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO roles (name, description) VALUES (?, ?)");
    foreach ($defaultRoles as $role) {
        $stmt->execute($role);
    }
    echo "Default roles inserted.\n";
    
    // Insert default permissions if they don't exist
    $defaultPermissions = [
        ['read_users', 'Can view user information'],
        ['write_users', 'Can create and edit users'],
        ['delete_users', 'Can delete users'],
        ['manage_roles', 'Can manage user roles'],
        ['view_analytics', 'Can view analytics and reports'],
        ['manage_settings', 'Can manage system settings']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO permissions (name, description) VALUES (?, ?)");
    foreach ($defaultPermissions as $permission) {
        $stmt->execute($permission);
    }
    echo "Default permissions inserted.\n";
    
    echo "All tables created and default data inserted successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

?>