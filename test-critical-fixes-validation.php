<?php
/**
 * Critical Fixes Validation Test
 * اختبار التحقق من الإصلاحات الحرجة
 */

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات الحرجة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        h1, h2, h3 { color: #333; }
        .test-button { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-button:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background: #f8f9fa; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار الإصلاحات الحرجة</h1>
        <p>التحقق من إصلاح خطأ SQL في الترحيل وتنفيذ واجهة إدارة الأدوار</p>

        <?php
        $testResults = [];
        
        try {
            $pdo = getPDOConnection();
            
            echo "<div class='section success'>";
            echo "<h2>✅ 1. اختبار الاتصال بقاعدة البيانات</h2>";
            echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
            echo "</div>";
            
            // Test 1: Database Migration SQL Syntax Fix
            echo "<div class='section'>";
            echo "<h2>🔧 2. اختبار إصلاح خطأ SQL في الترحيل</h2>";
            
            // Check if the fixed migration file exists
            $migrationFile = 'database/migrations/consolidate_livres_to_produits.sql';
            if (file_exists($migrationFile)) {
                $migrationContent = file_get_contents($migrationFile);
                
                // Check if the problematic prepared statement syntax is removed
                if (strpos($migrationContent, 'PREPARE stmt FROM @migrate_sql') === false) {
                    echo "<div class='success'>✅ تم إزالة بناء الجملة المشكل للـ prepared statement</div>";
                    $testResults['migration_syntax'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ لا يزال بناء الجملة المشكل موجود</div>";
                    $testResults['migration_syntax'] = 'FAIL';
                }
                
                // Check if the new compatible syntax is present
                if (strpos($migrationContent, 'INSERT IGNORE INTO produits') !== false) {
                    echo "<div class='success'>✅ تم استخدام بناء جملة متوافق مع MariaDB</div>";
                    $testResults['migration_compatibility'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ بناء الجملة الجديد غير موجود</div>";
                    $testResults['migration_compatibility'] = 'FAIL';
                }
                
                // Test the safe migration runner
                $safeMigrationFile = 'database/run_safe_migration.php';
                if (file_exists($safeMigrationFile)) {
                    echo "<div class='success'>✅ تم إنشاء أداة الترحيل الآمنة</div>";
                    $testResults['safe_migration'] = 'PASS';
                } else {
                    echo "<div class='warning'>⚠️ أداة الترحيل الآمنة غير موجودة</div>";
                    $testResults['safe_migration'] = 'WARNING';
                }
                
            } else {
                echo "<div class='error'>❌ ملف الترحيل غير موجود</div>";
                $testResults['migration_file'] = 'FAIL';
            }
            echo "</div>";
            
            // Test 2: Role Management Interface Implementation
            echo "<div class='section'>";
            echo "<h2>🛡️ 3. اختبار تنفيذ واجهة إدارة الأدوار</h2>";
            
            // Check if users-management.js has role management functions
            $jsFile = 'admin/js/users-management.js';
            if (file_exists($jsFile)) {
                $jsContent = file_get_contents($jsFile);
                
                // Check for role management functions
                $roleFunctions = [
                    'showRolesManagement' => 'عرض إدارة الأدوار',
                    'loadRolesManagementInterface' => 'تحميل واجهة إدارة الأدوار',
                    'loadRolesData' => 'تحميل بيانات الأدوار',
                    'showAddRoleModal' => 'عرض نافذة إضافة دور',
                    'saveRole' => 'حفظ الدور',
                    'editRole' => 'تعديل الدور',
                    'deleteRole' => 'حذف الدور'
                ];
                
                echo "<table>";
                echo "<tr><th>الوظيفة</th><th>الوصف</th><th>الحالة</th></tr>";
                
                foreach ($roleFunctions as $func => $desc) {
                    $exists = strpos($jsContent, "function $func") !== false;
                    $status = $exists ? 'PASS' : 'FAIL';
                    $statusClass = $exists ? 'status-pass' : 'status-fail';
                    $statusText = $exists ? '✅ موجود' : '❌ مفقود';
                    
                    echo "<tr>";
                    echo "<td>$func</td>";
                    echo "<td>$desc</td>";
                    echo "<td class='$statusClass'>$statusText</td>";
                    echo "</tr>";
                    
                    $testResults["role_function_$func"] = $status;
                }
                echo "</table>";
                
                // Check if the placeholder "قيد التطوير" is removed
                if (strpos($jsContent, 'قيد التطوير') === false) {
                    echo "<div class='success'>✅ تم إزالة رسالة 'قيد التطوير'</div>";
                    $testResults['placeholder_removed'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ لا تزال رسالة 'قيد التطوير' موجودة</div>";
                    $testResults['placeholder_removed'] = 'FAIL';
                }
                
            } else {
                echo "<div class='error'>❌ ملف JavaScript غير موجود</div>";
                $testResults['js_file'] = 'FAIL';
            }
            echo "</div>";
            
            // Test 3: API Endpoints
            echo "<div class='section'>";
            echo "<h2>🔗 4. اختبار نقاط النهاية للـ API</h2>";
            
            $apiEndpoints = [
                'php/api/roles.php' => 'API إدارة الأدوار',
                'php/api/users.php' => 'API إدارة المستخدمين',
                'api/users.php' => 'API المستخدمين القديم (محدث)'
            ];
            
            echo "<table>";
            echo "<tr><th>نقطة النهاية</th><th>الوصف</th><th>الحالة</th></tr>";
            
            foreach ($apiEndpoints as $endpoint => $description) {
                $exists = file_exists($endpoint);
                $status = $exists ? 'PASS' : 'FAIL';
                $statusClass = $exists ? 'status-pass' : 'status-fail';
                $statusText = $exists ? '✅ موجود' : '❌ مفقود';
                
                echo "<tr>";
                echo "<td>$endpoint</td>";
                echo "<td>$description</td>";
                echo "<td class='$statusClass'>$statusText</td>";
                echo "</tr>";
                
                $testResults["api_$endpoint"] = $status;
            }
            echo "</table>";
            echo "</div>";
            
            // Test 4: Database Structure
            echo "<div class='section'>";
            echo "<h2>🗄️ 5. اختبار هيكل قاعدة البيانات</h2>";
            
            // Check user_roles table
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_roles'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✅ جدول user_roles موجود</div>";
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
                $rolesCount = $stmt->fetch()['count'];
                echo "<div class='info'>📊 عدد الأدوار: $rolesCount</div>";
                
                $testResults['user_roles_table'] = 'PASS';
            } else {
                echo "<div class='error'>❌ جدول user_roles غير موجود</div>";
                $testResults['user_roles_table'] = 'FAIL';
            }
            
            // Check users table structure
            $stmt = $pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredColumns = ['username', 'first_name', 'last_name', 'role_id'];
            $missingColumns = array_diff($requiredColumns, $columns);
            
            if (empty($missingColumns)) {
                echo "<div class='success'>✅ جدول users يحتوي على جميع الأعمدة المطلوبة</div>";
                $testResults['users_table_structure'] = 'PASS';
            } else {
                echo "<div class='error'>❌ أعمدة مفقودة في جدول users: " . implode(', ', $missingColumns) . "</div>";
                $testResults['users_table_structure'] = 'FAIL';
            }
            echo "</div>";
            
            // Test Summary
            echo "<div class='section'>";
            echo "<h2>📊 6. ملخص النتائج</h2>";
            
            $totalTests = count($testResults);
            $passedTests = count(array_filter($testResults, function($result) { return $result === 'PASS'; }));
            $failedTests = count(array_filter($testResults, function($result) { return $result === 'FAIL'; }));
            $warningTests = count(array_filter($testResults, function($result) { return $result === 'WARNING'; }));
            
            echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
            echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; text-align: center;'>";
            echo "<h3 style='margin: 0; color: #155724;'>✅ نجح</h3>";
            echo "<p style='margin: 5px 0 0 0; font-size: 1.5rem; font-weight: bold; color: #155724;'>$passedTests</p>";
            echo "</div>";
            
            echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; text-align: center;'>";
            echo "<h3 style='margin: 0; color: #721c24;'>❌ فشل</h3>";
            echo "<p style='margin: 5px 0 0 0; font-size: 1.5rem; font-weight: bold; color: #721c24;'>$failedTests</p>";
            echo "</div>";
            
            echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; text-align: center;'>";
            echo "<h3 style='margin: 0; color: #856404;'>⚠️ تحذير</h3>";
            echo "<p style='margin: 5px 0 0 0; font-size: 1.5rem; font-weight: bold; color: #856404;'>$warningTests</p>";
            echo "</div>";
            
            echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; text-align: center;'>";
            echo "<h3 style='margin: 0; color: #0c5460;'>📊 المجموع</h3>";
            echo "<p style='margin: 5px 0 0 0; font-size: 1.5rem; font-weight: bold; color: #0c5460;'>$totalTests</p>";
            echo "</div>";
            echo "</div>";
            
            $successRate = round(($passedTests / $totalTests) * 100, 1);
            
            if ($successRate >= 90) {
                echo "<div class='success'>";
                echo "<h3>🎉 ممتاز! معدل النجاح: $successRate%</h3>";
                echo "<p>جميع الإصلاحات الحرجة تعمل بشكل صحيح</p>";
            } elseif ($successRate >= 70) {
                echo "<div class='warning'>";
                echo "<h3>⚠️ جيد - معدل النجاح: $successRate%</h3>";
                echo "<p>معظم الإصلاحات تعمل، لكن هناك بعض المشاكل التي تحتاج إلى إصلاح</p>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ يحتاج إلى تحسين - معدل النجاح: $successRate%</h3>";
                echo "<p>هناك مشاكل كبيرة تحتاج إلى إصلاح فوري</p>";
            }
            echo "</div>";
            echo "</div>";
            
            // Quick Links
            echo "<div class='section info'>";
            echo "<h3>🔗 روابط سريعة للاختبار</h3>";
            echo "<a href='database/run_safe_migration.php' class='test-button'>🔧 تشغيل الترحيل الآمن</a>";
            echo "<a href='admin/users-management-standalone.html' class='test-button'>👥 اختبار إدارة المستخدمين</a>";
            echo "<a href='admin/index.html' class='test-button'>🏠 لوحة التحكم الرئيسية</a>";
            echo "<a href='test-user-management-integration.php' class='test-button'>🧪 اختبار التكامل</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h2>❌ خطأ في الاختبار</h2>";
            echo "<p>حدث خطأ أثناء تشغيل الاختبارات: " . $e->getMessage() . "</p>";
            echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
