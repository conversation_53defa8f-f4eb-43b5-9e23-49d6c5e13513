<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات الأمان</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        #securitySettingsContent { border: 2px solid #ddd; border-radius: 10px; padding: 20px; margin-top: 20px; min-height: 400px; background: #f8f9fa; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔒 اختبار إعدادات الأمان</h1>
        <p>فحص تنفيذ وظائف إعدادات الأمان في لوحة التحكم</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="testFunctionAvailability()">
                <i class="fas fa-search"></i> فحص توفر الوظائف
            </button>
            <button class="test-button" onclick="testSecuritySettingsLoad()">
                <i class="fas fa-play"></i> اختبار تحميل الإعدادات
            </button>
            <button class="test-button" onclick="testDirectCall()">
                <i class="fas fa-cog"></i> اختبار الاستدعاء المباشر
            </button>
            <button class="test-button" onclick="testAPIConnection()">
                <i class="fas fa-link"></i> اختبار الاتصال بـ API
            </button>
            <button class="test-button" onclick="clearAll()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>

        <div id="testResults"></div>

        <!-- Container for security settings content -->
        <div id="securitySettingsContent">
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-shield-alt" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>منطقة اختبار إعدادات الأمان</h3>
                <p>سيتم تحميل محتوى إعدادات الأمان هنا</p>
            </div>
        </div>
    </div>

    <!-- Load the security settings script -->
    <script src="admin/js/security-settings.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAll() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('securitySettingsContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-shield-alt" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3>منطقة اختبار إعدادات الأمان</h3>
                    <p>سيتم تحميل محتوى إعدادات الأمان هنا</p>
                </div>
            `;
        }

        function testFunctionAvailability() {
            addResult('<h3>🔍 فحص توفر الوظائف:</h3>');
            
            const functions = [
                'loadSecuritySettingsContent',
                'showSecuritySettings', 
                'loadSecuritySettingsInterface',
                'switchSecurityTab',
                'loadAuthenticationTab',
                'saveAuthenticationSettings',
                'runSecurityAudit',
                'exportSecuritySettings',
                'calculateSecurityScore'
            ];

            let functionsAvailable = 0;
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} - متوفرة`, 'success');
                    functionsAvailable++;
                } else {
                    addResult(`❌ ${funcName} - غير متوفرة`, 'error');
                }
            });
            
            addResult(`📊 الوظائف المتوفرة: ${functionsAvailable}/${functions.length}`, 'info');
        }

        function testSecuritySettingsLoad() {
            addResult('<h3>🎭 اختبار تحميل إعدادات الأمان:</h3>');
            
            if (typeof loadSecuritySettingsContent === 'function') {
                addResult('✅ الوظيفة loadSecuritySettingsContent متوفرة', 'success');
                addResult('🔄 استدعاء الوظيفة...', 'info');
                
                try {
                    loadSecuritySettingsContent();
                    
                    setTimeout(() => {
                        const content = document.getElementById('securitySettingsContent').innerHTML;
                        addResult('📄 المحتوى بعد الاستدعاء:', 'info');
                        
                        if (content.includes('إعدادات الأمان')) {
                            addResult('✅ تم تحميل واجهة إعدادات الأمان بنجاح!', 'success');
                        } else if (content.includes('جاري تحميل')) {
                            addResult('⚠️ المحتوى في حالة تحميل...', 'warning');
                        } else {
                            addResult('⚠️ محتوى غير متوقع', 'warning');
                        }
                        
                        // Check for specific elements
                        if (content.includes('مستوى الأمان')) {
                            addResult('✅ لوحة معلومات الأمان موجودة', 'success');
                        }
                        
                        if (content.includes('المصادقة')) {
                            addResult('✅ تبويب المصادقة موجود', 'success');
                        }
                        
                        if (content.includes('التحكم في الوصول')) {
                            addResult('✅ تبويب التحكم في الوصول موجود', 'success');
                        }
                        
                    }, 1000);
                } catch (error) {
                    addResult(`❌ خطأ في استدعاء الوظيفة: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ الوظيفة loadSecuritySettingsContent غير متوفرة', 'error');
            }
        }

        function testDirectCall() {
            addResult('<h3>🎯 اختبار الاستدعاء المباشر:</h3>');
            
            try {
                if (typeof showSecuritySettings === 'function') {
                    addResult('✅ الوظيفة showSecuritySettings متوفرة', 'success');
                    addResult('🔄 استدعاء مباشر للوظيفة...', 'info');
                    
                    showSecuritySettings();
                    
                    setTimeout(() => {
                        const content = document.getElementById('securitySettingsContent').innerHTML;
                        if (content.includes('إعدادات الأمان')) {
                            addResult('✅ الاستدعاء المباشر نجح!', 'success');
                        } else {
                            addResult('⚠️ الاستدعاء المباشر لم ينتج المحتوى المتوقع', 'warning');
                        }
                    }, 1000);
                } else {
                    addResult('❌ الوظيفة showSecuritySettings غير متوفرة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في الاستدعاء المباشر: ${error.message}`, 'error');
            }
        }

        function testAPIConnection() {
            addResult('<h3>🔗 اختبار الاتصال بـ API:</h3>');
            
            fetch('php/api/security-settings.php?action=get_settings')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ API إعدادات الأمان متاح', 'success');
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(data => {
                    if (data.success) {
                        addResult('✅ API يعيد بيانات صحيحة', 'success');
                        addResult(`📊 البيانات المستلمة: ${JSON.stringify(data).substring(0, 100)}...`, 'info');
                    } else {
                        addResult('⚠️ API يعيد خطأ: ' + (data.message || 'غير محدد'), 'warning');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في الاتصال بـ API: ${error.message}`, 'error');
                });
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 اختبار تلقائي عند تحميل الصفحة:</h3>');
                testFunctionAvailability();
            }, 500);
        });
    </script>
</body>
</html>
