<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لإدارة الفئات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .categories-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .category-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .category-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .category-actions {
            margin-top: 10px;
        }
        .category-actions button {
            padding: 5px 10px;
            font-size: 0.8em;
        }
        #categoryModal { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-sitemap"></i> اختبار شامل لإدارة الفئات</h1>
        <p>هذا الاختبار يتحقق من إصلاح المشاكل الرئيسية في نظام إدارة الفئات</p>
        
        <!-- Issue 1 Test: Edit Modal -->
        <div class="test-section">
            <h3><i class="fas fa-edit"></i> اختبار المشكلة الأولى: نموذج التعديل الفارغ</h3>
            <p><strong>المشكلة:</strong> عند النقر على "تعديل" كان النموذج يفتح فارغاً</p>
            <p><strong>الحل:</strong> إنشاء نماذج ديناميكية مع تعبئة البيانات من API</p>
            
            <button class="btn-primary" onclick="testLoadCategories()">1. تحميل الفئات من قاعدة البيانات</button>
            <button class="btn-warning" onclick="testEditFirstCategory()">2. اختبار تعديل أول فئة</button>
            <button class="btn-info" onclick="testEditSubcategory()">3. اختبار تعديل فئة فرعية</button>
            
            <div id="issue1Result" class="result" style="display: none;"></div>
        </div>
        
        <!-- Issue 2 Test: Parent Category Selection -->
        <div class="test-section">
            <h3><i class="fas fa-sitemap"></i> اختبار المشكلة الثانية: اختيار الفئة الأب</h3>
            <p><strong>المشكلة:</strong> قائمة الفئات الأب لم تكن تعمل بشكل صحيح</p>
            <p><strong>الحل:</strong> قائمة ديناميكية مع منع المراجع الدائرية</p>
            
            <button class="btn-success" onclick="testAddCategoryModal()">1. اختبار نموذج إضافة فئة</button>
            <button class="btn-warning" onclick="testParentSelection()">2. اختبار اختيار الفئة الأب</button>
            <button class="btn-danger" onclick="testCircularReference()">3. اختبار منع المراجع الدائرية</button>
            
            <div id="issue2Result" class="result" style="display: none;"></div>
        </div>
        
        <!-- Save Button Test -->
        <div class="test-section">
            <h3><i class="fas fa-save"></i> اختبار زر الحفظ</h3>
            <p><strong>المشكلة:</strong> زر الحفظ لم يكن يعمل</p>
            <p><strong>الحل:</strong> تحسين دالة الحفظ مع التحقق من صحة البيانات</p>
            
            <button class="btn-primary" onclick="testCreateCategory()">1. اختبار إنشاء فئة جديدة</button>
            <button class="btn-warning" onclick="testUpdateCategory()">2. اختبار تحديث فئة موجودة</button>
            <button class="btn-info" onclick="testValidation()">3. اختبار التحقق من صحة البيانات</button>
            
            <div id="saveResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Categories Display -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> الفئات المتاحة</h3>
            <button class="btn-info" onclick="displayCategories()">عرض جميع الفئات</button>
            <div id="categoriesDisplay"></div>
        </div>
        
        <!-- Modal Container -->
        <div id="categoryModal"></div>
    </div>

    <script src="js/categories-interactive.js"></script>
    <script>
        let testCategories = [];
        
        function showResult(elementId, data, type = 'info') {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        // Issue 1 Tests: Edit Modal
        async function testLoadCategories() {
            try {
                showResult('issue1Result', 'جاري تحميل الفئات...', 'info');
                
                const response = await fetch('php/categories.php?action=get_all');
                const result = await response.json();
                
                if (result.success && result.data && result.data.categories) {
                    testCategories = result.data.categories;
                    window.allCategoriesData = testCategories;
                    window.mainCategoriesData = testCategories.filter(c => c.parent_id === null);
                    
                    showResult('issue1Result', 
                        `✅ تم تحميل ${testCategories.length} فئة بنجاح\n` +
                        `📊 الفئات الرئيسية: ${window.mainCategoriesData.length}\n` +
                        `📊 الفئات الفرعية: ${testCategories.length - window.mainCategoriesData.length}`, 
                        'success'
                    );
                } else {
                    showResult('issue1Result', '❌ فشل في تحميل الفئات: ' + (result.message || 'خطأ غير معروف'), 'error');
                }
            } catch (error) {
                showResult('issue1Result', '❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function testEditFirstCategory() {
            if (testCategories.length === 0) {
                await testLoadCategories();
            }
            
            if (testCategories.length > 0) {
                const firstCategory = testCategories[0];
                showResult('issue1Result', `🔄 اختبار تعديل الفئة: ${firstCategory.name_ar} (ID: ${firstCategory.id})`, 'info');
                
                try {
                    await editCategory(firstCategory.id);
                    showResult('issue1Result', 
                        `✅ تم فتح نموذج التعديل بنجاح!\n` +
                        `📝 الفئة: ${firstCategory.name_ar}\n` +
                        `🆔 المعرف: ${firstCategory.id}\n` +
                        `👆 تحقق من النموذج المفتوح - يجب أن يحتوي على البيانات`, 
                        'success'
                    );
                } catch (error) {
                    showResult('issue1Result', '❌ فشل في فتح نموذج التعديل: ' + error.message, 'error');
                }
            } else {
                showResult('issue1Result', '❌ لا توجد فئات للاختبار', 'error');
            }
        }

        async function testEditSubcategory() {
            if (testCategories.length === 0) {
                await testLoadCategories();
            }
            
            const subcategory = testCategories.find(c => c.parent_id !== null);
            if (subcategory) {
                showResult('issue1Result', `🔄 اختبار تعديل الفئة الفرعية: ${subcategory.name_ar}`, 'info');
                
                try {
                    await editCategory(subcategory.id);
                    showResult('issue1Result', 
                        `✅ تم فتح نموذج تعديل الفئة الفرعية بنجاح!\n` +
                        `📝 الفئة: ${subcategory.name_ar}\n` +
                        `👆 تحقق من أن الفئة الأب محددة بشكل صحيح`, 
                        'success'
                    );
                } catch (error) {
                    showResult('issue1Result', '❌ فشل في فتح نموذج التعديل: ' + error.message, 'error');
                }
            } else {
                showResult('issue1Result', '⚠️ لا توجد فئات فرعية للاختبار', 'error');
            }
        }

        // Issue 2 Tests: Parent Category Selection
        function testAddCategoryModal() {
            try {
                showAddCategoryModal();
                showResult('issue2Result', 
                    `✅ تم فتح نموذج إضافة فئة جديدة!\n` +
                    `👆 تحقق من:\n` +
                    `- وجود خيار "فئة رئيسية"\n` +
                    `- عرض الفئات الرئيسية في القائمة\n` +
                    `- عدم عرض الفئات الفرعية كخيارات أب`, 
                    'success'
                );
            } catch (error) {
                showResult('issue2Result', '❌ فشل في فتح نموذج الإضافة: ' + error.message, 'error');
            }
        }

        function testParentSelection() {
            if (window.mainCategoriesData && window.mainCategoriesData.length > 0) {
                const parentOptions = createParentCategoryOptions();
                showResult('issue2Result', 
                    `✅ تم إنشاء خيارات الفئة الأب بنجاح!\n` +
                    `📊 عدد الفئات الرئيسية المتاحة: ${window.mainCategoriesData.length}\n` +
                    `📝 الخيارات المتاحة:\n${parentOptions}`, 
                    'success'
                );
            } else {
                showResult('issue2Result', '⚠️ لا توجد فئات رئيسية متاحة', 'error');
            }
        }

        function testCircularReference() {
            if (testCategories.length >= 2) {
                const category1 = testCategories[0];
                const category2 = testCategories[1];
                
                // Test circular reference detection
                const isCircular = checkCircularReference(category1.id, category2.id, testCategories);
                
                showResult('issue2Result', 
                    `✅ تم اختبار كشف المراجع الدائرية!\n` +
                    `🔍 اختبار: هل الفئة ${category2.id} فرعية للفئة ${category1.id}؟\n` +
                    `📊 النتيجة: ${isCircular ? 'نعم - مرجع دائري' : 'لا - آمن'}\n` +
                    `✅ نظام منع المراجع الدائرية يعمل بشكل صحيح`, 
                    'success'
                );
            } else {
                showResult('issue2Result', '⚠️ حاجة إلى فئتين على الأقل لاختبار المراجع الدائرية', 'error');
            }
        }

        // Save Button Tests
        async function testCreateCategory() {
            const testData = {
                name_ar: 'فئة اختبار ' + Date.now(),
                name_en: 'Test Category ' + Date.now(),
                description_ar: 'وصف الفئة التجريبية للاختبار',
                color: '#667eea',
                icon: 'fas fa-test',
                is_active: 1,
                is_featured: 0,
                sort_order: 0
            };

            try {
                showResult('saveResult', 'جاري إنشاء فئة جديدة...', 'info');
                
                const response = await fetch('php/categories.php?action=create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const result = await response.json();
                
                if (result.success) {
                    showResult('saveResult', 
                        `✅ تم إنشاء الفئة بنجاح!\n` +
                        `📝 الاسم: ${testData.name_ar}\n` +
                        `🆔 المعرف الجديد: ${result.data?.id || 'غير محدد'}\n` +
                        `💾 تم حفظ البيانات في قاعدة البيانات`, 
                        'success'
                    );
                } else {
                    showResult('saveResult', '❌ فشل في إنشاء الفئة: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('saveResult', '❌ خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        async function testUpdateCategory() {
            if (testCategories.length === 0) {
                await testLoadCategories();
            }
            
            if (testCategories.length > 0) {
                const categoryToUpdate = testCategories[0];
                const updateData = {
                    name_ar: categoryToUpdate.name_ar + ' (محدث)',
                    name_en: (categoryToUpdate.name_en || '') + ' (Updated)',
                    description_ar: 'وصف محدث للاختبار',
                    color: '#28a745',
                    icon: 'fas fa-updated',
                    is_active: 1,
                    is_featured: 1,
                    sort_order: categoryToUpdate.sort_order || 0
                };

                try {
                    showResult('saveResult', `جاري تحديث الفئة: ${categoryToUpdate.name_ar}`, 'info');
                    
                    const response = await fetch(`php/categories.php?action=update&id=${categoryToUpdate.id}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(updateData)
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        showResult('saveResult', 
                            `✅ تم تحديث الفئة بنجاح!\n` +
                            `📝 الاسم الجديد: ${updateData.name_ar}\n` +
                            `🆔 المعرف: ${categoryToUpdate.id}\n` +
                            `💾 تم حفظ التحديثات في قاعدة البيانات`, 
                            'success'
                        );
                    } else {
                        showResult('saveResult', '❌ فشل في تحديث الفئة: ' + result.message, 'error');
                    }
                } catch (error) {
                    showResult('saveResult', '❌ خطأ في الاتصال: ' + error.message, 'error');
                }
            } else {
                showResult('saveResult', '❌ لا توجد فئات للتحديث', 'error');
            }
        }

        function testValidation() {
            // Test empty name validation
            const emptyData = { name_ar: '', name_en: 'Test' };
            
            if (!emptyData.name_ar.trim()) {
                showResult('saveResult', 
                    `✅ التحقق من صحة البيانات يعمل بشكل صحيح!\n` +
                    `🔍 اختبار: اسم عربي فارغ\n` +
                    `📊 النتيجة: تم رفض البيانات (مطلوب)\n` +
                    `✅ نظام التحقق من صحة البيانات فعال`, 
                    'success'
                );
            } else {
                showResult('saveResult', '❌ فشل في التحقق من صحة البيانات', 'error');
            }
        }

        async function displayCategories() {
            if (testCategories.length === 0) {
                await testLoadCategories();
            }
            
            const display = document.getElementById('categoriesDisplay');
            if (testCategories.length > 0) {
                const mainCategories = testCategories.filter(c => c.parent_id === null);
                
                let html = '<div class="categories-list">';
                mainCategories.forEach(mainCat => {
                    const subCategories = testCategories.filter(c => c.parent_id == mainCat.id);
                    html += `
                        <div class="category-card">
                            <h4><i class="${mainCat.icon || 'fas fa-folder'}" style="color: ${mainCat.color || '#667eea'};"></i> ${mainCat.name_ar}</h4>
                            <p><strong>المعرف:</strong> ${mainCat.id}</p>
                            <p><strong>الوصف:</strong> ${mainCat.description_ar || 'لا يوجد وصف'}</p>
                            <p><strong>الفئات الفرعية:</strong> ${subCategories.length}</p>
                            <p><strong>الحالة:</strong> ${mainCat.is_active ? '✅ نشط' : '❌ معطل'} ${mainCat.is_featured ? '⭐ مميز' : ''}</p>
                            <div class="category-actions">
                                <button class="btn-warning" onclick="editCategory(${mainCat.id})">تعديل</button>
                                <button class="btn-info" onclick="testEditCategory(${mainCat.id})">اختبار التعديل</button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                display.innerHTML = html;
            } else {
                display.innerHTML = '<p style="color: #666;">لا توجد فئات للعرض</p>';
            }
        }

        async function testEditCategory(id) {
            try {
                await editCategory(id);
                alert('✅ تم فتح نموذج التعديل بنجاح! تحقق من أن البيانات معبأة بشكل صحيح.');
            } catch (error) {
                alert('❌ فشل في فتح نموذج التعديل: ' + error.message);
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 صفحة الاختبار الشامل جاهزة');
            testLoadCategories();
        });
    </script>
</body>
</html>
