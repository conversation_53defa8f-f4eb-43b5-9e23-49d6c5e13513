<?php
/**
 * Fix All Database Tables
 * إصلاح جميع جداول قاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');

require_once 'config/db_env.php';

$results = [];
$results['timestamp'] = date('Y-m-d H:i:s');

try {
    // 1. Fix categories table
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        $createCategoriesSQL = "
            CREATE TABLE categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                name_ar VARCHAR(255) NOT NULL,
                description TEXT,
                description_ar TEXT,
                parent_id INT NULL,
                icon VARCHAR(100) DEFAULT 'fas fa-tag',
                color VARCHAR(7) DEFAULT '#3498db',
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                meta_title VARCHAR(255),
                meta_description TEXT,
                slug VARCHAR(255) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
                INDEX idx_parent (parent_id),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createCategoriesSQL);
        $results['categories_table'] = 'created';
        
        // Insert default categories
        $defaultCategories = [
            [1, 'Electronics', 'إلكترونيات', 'Electronic devices and gadgets', 'الأجهزة الإلكترونية والتقنية', NULL, 'fas fa-laptop', '#3498db', 1, 1, 'electronics'],
            [2, 'Clothing', 'ملابس', 'Fashion and clothing items', 'الأزياء والملابس', NULL, 'fas fa-tshirt', '#e74c3c', 2, 1, 'clothing'],
            [3, 'Home & Garden', 'منزل وحديقة', 'Home improvement and garden supplies', 'تحسين المنزل ومستلزمات الحديقة', NULL, 'fas fa-home', '#27ae60', 3, 1, 'home-garden'],
            [4, 'Sports', 'رياضة', 'Sports equipment and accessories', 'المعدات الرياضية والإكسسوارات', NULL, 'fas fa-dumbbell', '#f39c12', 4, 1, 'sports'],
            [5, 'Books', 'كتب', 'Books and educational materials', 'الكتب والمواد التعليمية', NULL, 'fas fa-book', '#9b59b6', 5, 1, 'books']
        ];
        
        $insertCatStmt = $pdo->prepare("
            INSERT INTO categories (id, name, name_ar, description, description_ar, parent_id, icon, color, sort_order, is_active, slug) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultCategories as $cat) {
            $insertCatStmt->execute($cat);
        }
        
        $results['categories_inserted'] = count($defaultCategories);
    } else {
        $results['categories_table'] = 'exists';
    }
    
    // 2. Fix payment_methods table
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_methods'");
    if ($stmt->rowCount() == 0) {
        $createPaymentSQL = "
            CREATE TABLE payment_methods (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                name_ar VARCHAR(255) NOT NULL,
                type ENUM('credit_card', 'bank_transfer', 'cash_on_delivery', 'digital_wallet', 'cryptocurrency') NOT NULL,
                provider VARCHAR(255),
                config JSON,
                fees_percentage DECIMAL(5,2) DEFAULT 0.00,
                fees_fixed DECIMAL(10,2) DEFAULT 0.00,
                min_amount DECIMAL(10,2) DEFAULT 0.00,
                max_amount DECIMAL(10,2) NULL,
                supported_currencies JSON,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_type (type),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createPaymentSQL);
        $results['payment_methods_table'] = 'created';
        
        // Insert default payment methods
        $defaultPayments = [
            [1, 'Cash on Delivery', 'الدفع عند الاستلام', 'cash_on_delivery', NULL, '{"delivery_fee": 200, "max_distance": 50}', 0.00, 200.00, 500.00, 50000.00, '["DZD"]', 1, 1],
            [2, 'Bank Transfer', 'تحويل بنكي', 'bank_transfer', 'CCP', '{"bank_name": "بريد الجزائر", "account_number": "*********", "iban": "DZ21 0001 0000 0123 4567 89"}', 0.00, 0.00, 1000.00, NULL, '["DZD"]', 1, 2],
            [3, 'Credit Card', 'بطاقة ائتمان', 'credit_card', 'Stripe', '{"api_key": "", "secret_key": "", "endpoint": "https://api.stripe.com", "test_mode": "true"}', 2.90, 30.00, 100.00, NULL, '["DZD", "USD", "EUR"]', 0, 3]
        ];
        
        $insertPayStmt = $pdo->prepare("
            INSERT INTO payment_methods (id, name, name_ar, type, provider, config, fees_percentage, fees_fixed, min_amount, max_amount, supported_currencies, is_active, sort_order) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultPayments as $payment) {
            $insertPayStmt->execute($payment);
        }
        
        $results['payment_methods_inserted'] = count($defaultPayments);
    } else {
        $results['payment_methods_table'] = 'exists';
    }
    
    // 3. Check general_settings table (should be fixed by previous script)
    $stmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM general_settings");
        $results['general_settings_count'] = $stmt->fetchColumn();
        $results['general_settings_table'] = 'exists';
    } else {
        $results['general_settings_table'] = 'missing';
    }
    
    // Final verification
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $results['all_tables'] = $tables;
    $results['tables_count'] = count($tables);
    
    $results['success'] = true;
    $results['message'] = 'All tables fixed successfully';
    
} catch (PDOException $e) {
    $results['success'] = false;
    $results['error'] = $e->getMessage();
}

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
