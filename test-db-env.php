<?php
/**
 * Database Connection Test with .env
 * اختبار الاتصال بقاعدة البيانات مع ملف .env
 */

header('Content-Type: application/json; charset=utf-8');

// Function to load .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        return [];
    }
    
    $env = [];
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    foreach ($lines as $line) {
        // Skip comments
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        // Parse key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            
            // Remove quotes if present
            if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                $value = substr($value, 1, -1);
            }
            
            $env[$key] = $value;
        }
    }
    
    return $env;
}

$results = [];
$results['timestamp'] = date('Y-m-d H:i:s');

// Test 1: Check if .env file exists
$envPath = '.env';
$results['env_file_exists'] = file_exists($envPath);

if ($results['env_file_exists']) {
    // Load environment variables
    $env = loadEnv($envPath);
    $results['env_loaded'] = !empty($env);
    $results['env_keys'] = array_keys($env);
    
    // Database configuration from .env
    $host = $env['DB_HOST'] ?? 'localhost';
    $dbname = $env['DB_DATABASE'] ?? 'mossab-landing-page';
    $username = $env['DB_USERNAME'] ?? 'root';
    $password = $env['DB_PASSWORD'] ?? '';
    $port = $env['DB_PORT'] ?? 3307;
    $charset = $env['DB_CHARSET'] ?? 'utf8mb4';
    
    $results['db_config'] = [
        'host' => $host,
        'port' => $port,
        'database' => $dbname,
        'username' => $username,
        'password_set' => !empty($password),
        'charset' => $charset
    ];
} else {
    $results['error'] = '.env file not found';
    echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

// Test 2: Check if MySQL extension is loaded
$results['mysql_extension'] = extension_loaded('pdo_mysql');

// Test 3: Try to connect to MySQL server (without database)
try {
    $pdo = new PDO("mysql:host=$host;port=$port;charset=$charset", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $results['mysql_connection'] = true;
    $results['mysql_version'] = $pdo->getAttribute(PDO::ATTR_SERVER_VERSION);
} catch (PDOException $e) {
    $results['mysql_connection'] = false;
    $results['mysql_error'] = $e->getMessage();
}

// Test 4: Try to create database
if ($results['mysql_connection']) {
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET $charset COLLATE {$charset}_unicode_ci");
        $results['database_creation'] = true;
    } catch (PDOException $e) {
        $results['database_creation'] = false;
        $results['database_creation_error'] = $e->getMessage();
    }
}

// Test 5: Try to connect to specific database
if ($results['mysql_connection']) {
    try {
        $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=$charset", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $results['database_connection'] = true;
    } catch (PDOException $e) {
        $results['database_connection'] = false;
        $results['database_connection_error'] = $e->getMessage();
    }
}

// Test 6: Check existing tables
if (isset($pdo) && $results['database_connection']) {
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $results['existing_tables'] = $tables;
        $results['tables_count'] = count($tables);
    } catch (PDOException $e) {
        $results['tables_check_error'] = $e->getMessage();
    }
}

// Test 7: Check required tables
$requiredTables = ['general_settings', 'categories', 'payment_methods'];
$results['required_tables'] = [];

if (isset($pdo) && $results['database_connection']) {
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['required_tables'][$table] = [
                'exists' => true,
                'columns' => array_column($columns, 'Field')
            ];
        } catch (PDOException $e) {
            $results['required_tables'][$table] = [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}

// Overall status
$results['overall_status'] = $results['mysql_connection'] && $results['database_connection'];

// Recommendations
$results['recommendations'] = [];

if (!$results['env_file_exists']) {
    $results['recommendations'][] = 'إنشاء ملف .env مع إعدادات قاعدة البيانات';
}

if (!$results['mysql_extension']) {
    $results['recommendations'][] = 'تأكد من تثبيت PHP MySQL extension';
}

if (!$results['mysql_connection']) {
    $results['recommendations'][] = 'تأكد من تشغيل خادم MySQL على المنفذ ' . $port;
    $results['recommendations'][] = 'تحقق من إعدادات الاتصال في ملف .env';
}

$missingTables = [];
if (isset($results['required_tables'])) {
    foreach ($results['required_tables'] as $table => $info) {
        if (!$info['exists']) {
            $missingTables[] = $table;
        }
    }
}

if (!empty($missingTables)) {
    $results['recommendations'][] = 'قم بتشغيل سكريبت إعداد قاعدة البيانات لإنشاء الجداول المفقودة: ' . implode(', ', $missingTables);
}

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
