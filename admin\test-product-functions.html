<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف إدارة المنتجات</title>
    <link rel="stylesheet" href="css/multi-user-admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .function-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .function-test-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f8f9fa;
        }
        .function-test-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-available {
            background: #28a745;
        }
        .status-unavailable {
            background: #dc3545;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            direction: ltr;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-cogs"></i> اختبار وظائف إدارة المنتجات</h1>
        <p>هذه الصفحة تختبر جميع وظائف إدارة المنتجات للتأكد من عدم وجود أخطاء ReferenceError.</p>

        <!-- Function Availability Test -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> اختبار توفر الوظائف</h3>
            <p>فحص ما إذا كانت جميع وظائف إدارة المنتجات متاحة ومعرفة بشكل صحيح</p>
            <button class="test-button" onclick="testFunctionAvailability()">فحص توفر الوظائف</button>
            <div id="availabilityTestResult"></div>
        </div>

        <!-- Function Testing Grid -->
        <div class="test-section">
            <h3><i class="fas fa-play"></i> اختبار الوظائف الفعلية</h3>
            <p>اختبار كل وظيفة بشكل منفصل للتأكد من عملها الصحيح</p>
            
            <div class="function-test-grid">
                <div class="function-test-card">
                    <h4><i class="fas fa-edit"></i> تعديل المنتج</h4>
                    <p>اختبار وظيفة editProduct</p>
                    <button class="test-button" onclick="testEditProduct()">اختبار</button>
                    <div id="editProductStatus"></div>
                </div>
                
                <div class="function-test-card">
                    <h4><i class="fas fa-eye"></i> عرض المنتج</h4>
                    <p>اختبار وظيفة viewProduct</p>
                    <button class="test-button" onclick="testViewProduct()">اختبار</button>
                    <div id="viewProductStatus"></div>
                </div>
                
                <div class="function-test-card">
                    <h4><i class="fas fa-trash"></i> حذف المنتج</h4>
                    <p>اختبار وظيفة deleteProduct</p>
                    <button class="test-button" onclick="testDeleteProduct()">اختبار</button>
                    <div id="deleteProductStatus"></div>
                </div>
                
                <div class="function-test-card">
                    <h4><i class="fas fa-rocket"></i> عرض صفحة الهبوط</h4>
                    <p>اختبار وظيفة viewLandingPage</p>
                    <button class="test-button" onclick="testViewLandingPage()">اختبار</button>
                    <div id="viewLandingPageStatus"></div>
                </div>
            </div>
        </div>

        <!-- ReferenceError Prevention Test -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> اختبار منع أخطاء ReferenceError</h3>
            <p>محاكاة النقر على أزرار المنتجات للتأكد من عدم حدوث أخطاء ReferenceError</p>
            <button class="test-button" onclick="testReferenceErrorPrevention()">اختبار منع الأخطاء</button>
            <div id="referenceErrorTestResult"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> مخرجات وحدة التحكم</h3>
            <div id="consoleOutput" class="console-output">جاهز لعرض مخرجات الاختبارات...</div>
        </div>

        <!-- Run All Tests -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" style="background: #28a745; font-size: 16px; padding: 15px 30px;" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
        </div>
    </div>

    <!-- Load the JavaScript files in the same order as the main page -->
    <script src="js/product-management-functions.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/products-pagination.js"></script>

    <script>
        // Console capture for testing
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        let consoleOutput = '';

        function captureConsole() {
            console.log = function(...args) {
                consoleOutput += '[LOG] ' + args.join(' ') + '\n';
                originalConsole.log.apply(console, args);
                updateConsoleDisplay();
            };

            console.error = function(...args) {
                consoleOutput += '[ERROR] ' + args.join(' ') + '\n';
                originalConsole.error.apply(console, args);
                updateConsoleDisplay();
            };

            console.warn = function(...args) {
                consoleOutput += '[WARN] ' + args.join(' ') + '\n';
                originalConsole.warn.apply(console, args);
                updateConsoleDisplay();
            };
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('consoleOutput');
            if (consoleDiv) {
                consoleDiv.textContent = consoleOutput || 'لا توجد مخرجات...';
                consoleDiv.scrollTop = consoleDiv.scrollHeight;
            }
        }

        function clearConsole() {
            consoleOutput = '';
            updateConsoleDisplay();
        }

        // Test functions
        function testFunctionAvailability() {
            const resultDiv = document.getElementById('availabilityTestResult');
            const functions = [
                { name: 'editProduct', func: window.editProduct },
                { name: 'viewProduct', func: window.viewProduct },
                { name: 'deleteProduct', func: window.deleteProduct },
                { name: 'viewLandingPage', func: window.viewLandingPage }
            ];

            let results = '<h4>نتائج فحص توفر الوظائف:</h4>';
            let allAvailable = true;

            functions.forEach(test => {
                const isAvailable = typeof test.func === 'function';
                const statusClass = isAvailable ? 'test-success' : 'test-error';
                const statusIcon = isAvailable ? '✅' : '❌';
                
                results += `<div class="${statusClass}">${statusIcon} ${test.name}: ${isAvailable ? 'متاحة' : 'غير متاحة'}</div>`;
                
                if (!isAvailable) allAvailable = false;
            });

            if (allAvailable) {
                results += '<div class="test-success"><strong>🎉 جميع الوظائف متاحة - لا توجد مشاكل ReferenceError!</strong></div>';
            } else {
                results += '<div class="test-error"><strong>⚠️ بعض الوظائف غير متاحة - قد تحدث أخطاء ReferenceError</strong></div>';
            }

            resultDiv.innerHTML = results;
        }

        function testEditProduct() {
            const statusDiv = document.getElementById('editProductStatus');
            try {
                if (typeof window.editProduct === 'function') {
                    console.log('Testing editProduct function...');
                    // Don't actually call it, just verify it exists
                    statusDiv.innerHTML = '<span class="status-indicator status-available"></span>متاحة';
                } else {
                    statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>غير متاحة';
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>خطأ: ' + error.message;
            }
        }

        function testViewProduct() {
            const statusDiv = document.getElementById('viewProductStatus');
            try {
                if (typeof window.viewProduct === 'function') {
                    console.log('Testing viewProduct function...');
                    statusDiv.innerHTML = '<span class="status-indicator status-available"></span>متاحة';
                } else {
                    statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>غير متاحة';
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>خطأ: ' + error.message;
            }
        }

        function testDeleteProduct() {
            const statusDiv = document.getElementById('deleteProductStatus');
            try {
                if (typeof window.deleteProduct === 'function') {
                    console.log('Testing deleteProduct function...');
                    statusDiv.innerHTML = '<span class="status-indicator status-available"></span>متاحة';
                } else {
                    statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>غير متاحة';
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>خطأ: ' + error.message;
            }
        }

        function testViewLandingPage() {
            const statusDiv = document.getElementById('viewLandingPageStatus');
            try {
                if (typeof window.viewLandingPage === 'function') {
                    console.log('Testing viewLandingPage function...');
                    statusDiv.innerHTML = '<span class="status-indicator status-available"></span>متاحة';
                } else {
                    statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>غير متاحة';
                }
            } catch (error) {
                statusDiv.innerHTML = '<span class="status-indicator status-unavailable"></span>خطأ: ' + error.message;
            }
        }

        function testReferenceErrorPrevention() {
            const resultDiv = document.getElementById('referenceErrorTestResult');
            
            let results = '<h4>نتائج اختبار منع أخطاء ReferenceError:</h4>';
            let allPassed = true;

            const buttonTests = [
                { name: 'زر تعديل المنتج', func: 'editProduct' },
                { name: 'زر عرض المنتج', func: 'viewProduct' },
                { name: 'زر حذف المنتج', func: 'deleteProduct' },
                { name: 'زر عرض صفحة الهبوط', func: 'viewLandingPage' }
            ];

            buttonTests.forEach(test => {
                try {
                    if (typeof window[test.func] === 'function') {
                        results += `<div class="test-success">✅ ${test.name}: لن يحدث ReferenceError</div>`;
                    } else {
                        results += `<div class="test-error">❌ ${test.name}: سيحدث ReferenceError</div>`;
                        allPassed = false;
                    }
                } catch (error) {
                    results += `<div class="test-error">❌ ${test.name}: خطأ - ${error.message}</div>`;
                    allPassed = false;
                }
            });

            if (allPassed) {
                results += '<div class="test-success"><strong>🎉 تم منع جميع أخطاء ReferenceError بنجاح!</strong></div>';
                results += '<div class="test-success">✅ جميع أزرار المنتجات ستعمل بشكل صحيح</div>';
            } else {
                results += '<div class="test-error"><strong>⚠️ لا تزال هناك مشاكل ReferenceError</strong></div>';
                results += '<div class="test-error">❌ بعض أزرار المنتجات قد لا تعمل</div>';
            }

            resultDiv.innerHTML = results;
        }

        function runAllTests() {
            clearConsole();
            console.log('🚀 بدء تشغيل جميع اختبارات وظائف إدارة المنتجات...');
            
            testFunctionAvailability();
            testEditProduct();
            testViewProduct();
            testDeleteProduct();
            testViewLandingPage();
            testReferenceErrorPrevention();
            
            console.log('✅ انتهاء جميع الاختبارات.');
        }

        // Initialize console capture
        captureConsole();
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
