<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من إصلاح التنقل</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        h1, h2, h3 { color: #333; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; cursor: pointer; border: none; }
        .test-button:hover { background: #0056b3; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: white; border: 1px solid #ddd; border-radius: 10px; padding: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-left: 8px; }
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ التحقق من إصلاح التنقل في لوحة التحكم</h1>
        <p>فحص شامل للتأكد من ظهور جميع عناصر القائمة بشكل صحيح</p>

        <div class="section info">
            <h3>🔧 الإصلاحات المُطبقة:</h3>
            <ul>
                <li>✅ إضافة "إدارة الاشتراكات" و "إعدادات الأمان" إلى قائمة الأقسام</li>
                <li>✅ توسيع قائمة إعدادات الإدارة بشكل افتراضي</li>
                <li>✅ إضافة JavaScript لتوسيع القائمة عند تحميل الصفحة</li>
                <li>✅ تحديث CSS لإظهار القائمة الفرعية</li>
                <li>✅ تدوير السهم لإظهار حالة التوسيع</li>
            </ul>
        </div>

        <div class="section">
            <h3>🧪 اختبارات التحقق:</h3>
            <button class="test-button" onclick="testMenuVisibility()">
                <i class="fas fa-eye"></i> فحص ظهور القائمة
            </button>
            <button class="test-button" onclick="testMenuExpansion()">
                <i class="fas fa-expand-arrows-alt"></i> اختبار التوسيع
            </button>
            <button class="test-button" onclick="testMenuItems()">
                <i class="fas fa-list"></i> فحص عناصر القائمة
            </button>
            <button class="test-button" onclick="testNavigation()">
                <i class="fas fa-mouse-pointer"></i> اختبار التنقل
            </button>
            <button class="test-button" onclick="openAdminPanel()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
        </div>

        <div id="testResults"></div>

        <div class="test-grid" id="testGrid">
            <!-- Test results will be populated here -->
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function addTestCard(title, status, details) {
            const grid = document.getElementById('testGrid');
            const statusClass = status === 'pass' ? 'status-pass' : status === 'fail' ? 'status-fail' : 'status-warning';
            const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
            
            const card = document.createElement('div');
            card.className = 'test-card';
            card.innerHTML = `
                <h4>${statusIcon} ${title} <span class="status-indicator ${statusClass}"></span></h4>
                <div>${details}</div>
            `;
            grid.appendChild(card);
        }

        function openAdminPanel() {
            window.open('admin/index.html', '_blank');
        }

        function testMenuVisibility() {
            addResult('<h3>👁️ اختبار ظهور القائمة:</h3>');
            
            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    // Check for admin settings menu structure
                    if (html.includes('admin-settings-menu')) {
                        addTestCard('هيكل القائمة', 'pass', 'عنصر admin-settings-menu موجود في HTML');
                    } else {
                        addTestCard('هيكل القائمة', 'fail', 'عنصر admin-settings-menu مفقود');
                    }

                    // Check for submenu
                    if (html.includes('admin-settings-submenu')) {
                        addTestCard('القائمة الفرعية', 'pass', 'عنصر admin-settings-submenu موجود');
                    } else {
                        addTestCard('القائمة الفرعية', 'fail', 'عنصر admin-settings-submenu مفقود');
                    }

                    // Check for arrow indicator
                    if (html.includes('admin-settings-arrow')) {
                        addTestCard('مؤشر السهم', 'pass', 'سهم التوسيع موجود');
                    } else {
                        addTestCard('مؤشر السهم', 'fail', 'سهم التوسيع مفقود');
                    }

                    addResult('✅ تم فحص هيكل القائمة', 'success');
                })
                .catch(error => {
                    addResult(`❌ خطأ في فحص القائمة: ${error.message}`, 'error');
                });
        }

        function testMenuExpansion() {
            addResult('<h3>🔍 اختبار آلية التوسيع:</h3>');
            
            // Check CSS files for expansion rules
            Promise.all([
                fetch('admin/css/admin.css').then(r => r.text()),
                fetch('admin/css/admin-settings-menu-enhanced.css').then(r => r.text())
            ]).then(([adminCSS, enhancedCSS]) => {
                // Check for max-height expansion
                if (enhancedCSS.includes('max-height: 400px !important')) {
                    addTestCard('توسيع افتراضي', 'pass', 'CSS يحتوي على قاعدة التوسيع الافتراضي');
                } else if (adminCSS.includes('max-height: 350px') || enhancedCSS.includes('max-height: 400px')) {
                    addTestCard('توسيع افتراضي', 'warning', 'آلية التوسيع موجودة لكن قد تحتاج تفعيل');
                } else {
                    addTestCard('توسيع افتراضي', 'fail', 'آلية التوسيع مفقودة');
                }

                // Check for arrow rotation
                if (enhancedCSS.includes('transform: rotate(180deg)')) {
                    addTestCard('تدوير السهم', 'pass', 'CSS يحتوي على قاعدة تدوير السهم');
                } else {
                    addTestCard('تدوير السهم', 'fail', 'قاعدة تدوير السهم مفقودة');
                }

                addResult('✅ تم فحص آلية التوسيع', 'success');
            }).catch(error => {
                addResult(`❌ خطأ في فحص CSS: ${error.message}`, 'error');
            });
        }

        function testMenuItems() {
            addResult('<h3>📋 اختبار عناصر القائمة:</h3>');
            
            const expectedItems = [
                { id: 'securitySettings', name: 'إعدادات الأمان', icon: 'fas fa-shield-alt' },
                { id: 'subscriptionsManagement', name: 'إدارة الاشتراكات', icon: 'fas fa-crown' }
            ];

            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    expectedItems.forEach(item => {
                        if (html.includes(`data-section="${item.id}"`)) {
                            addTestCard(item.name, 'pass', `عنصر القائمة ${item.id} موجود في HTML`);
                        } else {
                            addTestCard(item.name, 'fail', `عنصر القائمة ${item.id} مفقود`);
                        }
                    });

                    // Check for all admin settings items
                    const allItems = ['generalSettings', 'paymentSettings', 'categories', 'usersManagement', 'rolesManagement', 'storeSettings', 'storesManagement', 'securitySettings', 'subscriptionsManagement'];
                    let foundCount = 0;
                    
                    allItems.forEach(item => {
                        if (html.includes(`data-section="${item}"`)) {
                            foundCount++;
                        }
                    });

                    addTestCard('إجمالي العناصر', foundCount === allItems.length ? 'pass' : 'warning', 
                        `${foundCount}/${allItems.length} عنصر موجود`);

                    addResult(`✅ تم فحص ${expectedItems.length} عنصر جديد`, 'success');
                })
                .catch(error => {
                    addResult(`❌ خطأ في فحص العناصر: ${error.message}`, 'error');
                });
        }

        function testNavigation() {
            addResult('<h3>🧭 اختبار وظائف التنقل:</h3>');
            
            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    // Check for JavaScript functions
                    if (html.includes('toggleAdminSettings')) {
                        addTestCard('وظيفة التبديل', 'pass', 'دالة toggleAdminSettings موجودة');
                    } else {
                        addTestCard('وظيفة التبديل', 'fail', 'دالة toggleAdminSettings مفقودة');
                    }

                    // Check for DOMContentLoaded expansion
                    if (html.includes('DOMContentLoaded') && html.includes('expanded')) {
                        addTestCard('التوسيع التلقائي', 'pass', 'كود التوسيع التلقائي موجود');
                    } else {
                        addTestCard('التوسيع التلقائي', 'warning', 'قد لا يتم التوسيع تلقائياً');
                    }

                    // Check for enhanced menu script
                    if (html.includes('admin-settings-menu-enhanced.js')) {
                        addTestCard('السكريبت المحسن', 'pass', 'ملف JavaScript المحسن مُحمل');
                    } else {
                        addTestCard('السكريبت المحسن', 'fail', 'ملف JavaScript المحسن غير مُحمل');
                    }

                    addResult('✅ تم فحص وظائف التنقل', 'success');
                })
                .catch(error => {
                    addResult(`❌ خطأ في فحص التنقل: ${error.message}`, 'error');
                });
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 تشغيل الاختبارات التلقائية:</h3>');
                testMenuVisibility();
                setTimeout(() => testMenuExpansion(), 500);
                setTimeout(() => testMenuItems(), 1000);
                setTimeout(() => testNavigation(), 1500);
            }, 500);
        });
    </script>
</body>
</html>
