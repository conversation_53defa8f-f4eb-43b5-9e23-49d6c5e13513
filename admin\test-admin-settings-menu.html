<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>اختبار قائمة إعدادات الإدارة</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link rel="stylesheet" href="css/admin.css" />
    <link rel="stylesheet" href="css/admin-settings-menu-enhanced.css" />
    <style>
      body {
        margin: 0;
        padding: 20px;
        background: #f8f9fa;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .test-container {
        max-width: 400px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .test-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .test-sidebar {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .test-nav {
        padding: 20px 0;
      }

      .test-nav ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .status-indicator {
        padding: 10px 20px;
        margin: 10px;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
      }

      .status-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="test-header">
        <h2><i class="fas fa-cogs"></i> اختبار قائمة إعدادات الإدارة</h2>
        <p>اختبار وظائف القائمة القابلة للطي</p>
      </div>

      <div class="status-indicator status-info">
        <i class="fas fa-info-circle"></i> اضغط على "إعدادات الإدارة" لاختبار
        القائمة
      </div>

      <div class="test-sidebar">
        <nav class="test-nav">
          <ul>
            <li class="admin-settings-menu">
              <div
                class="admin-settings-header"
                onclick="toggleAdminSettings()"
              >
                <i class="fas fa-cogs"></i>
                <span>إعدادات الإدارة</span>
                <i class="fas fa-chevron-down admin-settings-arrow"></i>
              </div>
              <ul class="admin-settings-submenu">
                <li
                  data-section="generalSettings"
                  onclick="testMenuClick('الإعدادات العامة')"
                >
                  <i class="fas fa-cog"></i>
                  <span>الإعدادات العامة</span>
                </li>
                <li
                  data-section="paymentSettings"
                  onclick="testMenuClick('إعدادات الدفع')"
                >
                  <i class="fas fa-credit-card"></i>
                  <span>إعدادات الدفع</span>
                </li>
                <li
                  data-section="categories"
                  onclick="testMenuClick('إدارة الفئات')"
                >
                  <i class="fas fa-tags"></i>
                  <span>إدارة الفئات</span>
                </li>
                <li
                  data-section="storeSettings"
                  onclick="testMenuClick('إعدادات المتجر')"
                >
                  <i class="fas fa-store"></i>
                  <span>إعدادات المتجر</span>
                </li>
                <li
                  data-section="securitySettings"
                  onclick="testMenuClick('الأمان')"
                >
                  <i class="fas fa-shield-alt"></i>
                  <span>الأمان</span>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
      </div>

      <div
        id="testResult"
        class="status-indicator status-info"
        style="display: none"
      >
        <i class="fas fa-mouse-pointer"></i> تم النقر على:
        <span id="clickedItem"></span>
      </div>
    </div>

    <script>
      // Admin Settings Collapsible Menu Functions
      function toggleAdminSettings() {
        const menu = document.querySelector(".admin-settings-menu");
        const submenu = document.querySelector(".admin-settings-submenu");

        if (menu.classList.contains("expanded")) {
          menu.classList.remove("expanded");
          submenu.style.maxHeight = "0";
          console.log("Menu collapsed");

          // Update status
          const statusDiv = document.querySelector(".status-indicator");
          statusDiv.className = "status-indicator status-info";
          statusDiv.innerHTML =
            '<i class="fas fa-info-circle"></i> تم إغلاق القائمة - اضغط مرة أخرى للفتح';
        } else {
          menu.classList.add("expanded");
          submenu.style.maxHeight = submenu.scrollHeight + "px";
          console.log("Menu expanded");

          // Update status
          const statusDiv = document.querySelector(".status-indicator");
          statusDiv.className = "status-indicator status-success";
          statusDiv.innerHTML =
            '<i class="fas fa-check-circle"></i> تم فتح القائمة - اختر أحد الخيارات';
        }
      }

      function testMenuClick(itemName) {
        // Remove active class from all submenu items
        document
          .querySelectorAll(".admin-settings-submenu li")
          .forEach((item) => {
            item.classList.remove("active");
          });

        // Add active class to clicked item
        event.target.closest("li").classList.add("active");

        // Show result
        const resultDiv = document.getElementById("testResult");
        const clickedItem = document.getElementById("clickedItem");

        clickedItem.textContent = itemName;
        resultDiv.style.display = "block";
        resultDiv.className = "status-indicator status-success";
        resultDiv.innerHTML = `<i class="fas fa-check-circle"></i> تم النقر على: ${itemName}`;

        console.log("Clicked on:", itemName);
      }

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Admin Settings Menu Test initialized");

        // Add click handlers for submenu items
        const submenuItems = document.querySelectorAll(
          ".admin-settings-submenu li"
        );
        submenuItems.forEach((item) => {
          item.addEventListener("click", function (e) {
            e.stopPropagation();

            // Remove active class from all submenu items
            submenuItems.forEach((i) => i.classList.remove("active"));
            // Add active class to clicked item
            this.classList.add("active");
          });
        });
      });
    </script>
  </body>
</html>
