/**
 * Enhanced Admin Settings Menu Controller
 * تحكم محسن في قائمة إعدادات الإدارة مع رسوم متحركة متقدمة
 */

class EnhancedAdminSettingsMenu {
    constructor() {
        this.menu = null;
        this.submenu = null;
        this.header = null;
        this.arrow = null;
        this.submenuItems = [];
        this.isExpanded = false;
        this.isAnimating = false;
        this.adminSettingsSections = ['generalSettings', 'paymentSettings', 'categories', 'storeSettings', 'securitySettings'];
        
        this.init();
    }
    
    init() {
        console.log('🚀 Initializing Enhanced Admin Settings Menu...');
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.menu = document.querySelector('.admin-settings-menu');
        this.submenu = document.querySelector('.admin-settings-submenu');
        this.header = document.querySelector('.admin-settings-header');
        this.arrow = document.querySelector('.admin-settings-arrow');
        this.submenuItems = document.querySelectorAll('.admin-settings-submenu li');
        
        if (!this.menu || !this.submenu || !this.header) {
            console.warn('⚠️ Admin settings menu elements not found');
            return;
        }
        
        this.bindEvents();
        this.addEnhancedAnimations();
        this.checkCurrentSection();
        
        console.log('✅ Enhanced Admin Settings Menu initialized successfully');
    }
    
    bindEvents() {
        // Header click event
        this.header.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });
        
        // Submenu items click events
        this.submenuItems.forEach((item, index) => {
            item.addEventListener('click', (e) => this.handleSubmenuClick(e, item));
            
            // Add staggered animation delay
            item.style.transitionDelay = `${index * 0.05}s`;
        });
        
        // Keyboard navigation
        this.header.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggle();
            }
        });
        
        // Add accessibility attributes
        this.header.setAttribute('role', 'button');
        this.header.setAttribute('aria-expanded', 'false');
        this.header.setAttribute('tabindex', '0');
        this.submenu.setAttribute('role', 'menu');
    }
    
    addEnhancedAnimations() {
        // Add ripple effect to header
        this.header.addEventListener('click', (e) => {
            this.createRippleEffect(e, this.header);
        });
        
        // Add hover effects to submenu items
        this.submenuItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                this.animateItemHover(item, true);
            });
            
            item.addEventListener('mouseleave', () => {
                this.animateItemHover(item, false);
            });
        });
    }
    
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    
    animateItemHover(item, isHover) {
        const icon = item.querySelector('i');
        const span = item.querySelector('span');
        
        if (isHover) {
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
            if (span) {
                span.style.transform = 'translateX(-2px)';
            }
        } else {
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
            if (span) {
                span.style.transform = 'translateX(0)';
            }
        }
    }
    
    async toggle() {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        
        if (this.isExpanded) {
            await this.collapse();
        } else {
            await this.expand();
        }
        
        this.isAnimating = false;
    }
    
    async expand() {
        if (!this.menu || !this.submenu) return;
        
        console.log('📈 Expanding admin settings menu...');
        
        // Update states
        this.isExpanded = true;
        this.menu.classList.add('expanded');
        this.header.setAttribute('aria-expanded', 'true');
        
        // Animate arrow
        if (this.arrow) {
            this.arrow.style.transform = 'rotate(180deg)';
        }
        
        // Calculate and set max height
        const scrollHeight = this.submenu.scrollHeight;
        this.submenu.style.maxHeight = scrollHeight + 'px';
        
        // Animate submenu items with stagger
        await this.animateSubmenuItems(true);
        
        console.log('✅ Admin settings menu expanded');
    }
    
    async collapse() {
        if (!this.menu || !this.submenu) return;
        
        console.log('📉 Collapsing admin settings menu...');
        
        // Animate submenu items out first
        await this.animateSubmenuItems(false);
        
        // Update states
        this.isExpanded = false;
        this.menu.classList.remove('expanded');
        this.header.setAttribute('aria-expanded', 'false');
        
        // Animate arrow
        if (this.arrow) {
            this.arrow.style.transform = 'rotate(0deg)';
        }
        
        // Collapse submenu
        this.submenu.style.maxHeight = '0';
        
        console.log('✅ Admin settings menu collapsed');
    }
    
    async animateSubmenuItems(show) {
        const items = Array.from(this.submenuItems);
        
        if (show) {
            // Staggered entrance animation
            for (let i = 0; i < items.length; i++) {
                setTimeout(() => {
                    items[i].style.opacity = '1';
                    items[i].style.transform = 'translateX(0) translateY(0)';
                }, i * 50);
            }
        } else {
            // Staggered exit animation
            for (let i = items.length - 1; i >= 0; i--) {
                setTimeout(() => {
                    items[i].style.opacity = '0';
                    items[i].style.transform = 'translateX(10px) translateY(-5px)';
                }, (items.length - 1 - i) * 30);
            }
            
            // Wait for animation to complete
            await new Promise(resolve => setTimeout(resolve, items.length * 30 + 100));
        }
    }
    
    handleSubmenuClick(e, item) {
        e.preventDefault();
        e.stopPropagation();
        
        // Create click effect
        this.createClickEffect(item);
        
        // Remove active class from all submenu items
        this.submenuItems.forEach(i => i.classList.remove('active'));
        
        // Add active class to clicked item
        item.classList.add('active');
        
        // Handle section switching
        const sectionId = item.getAttribute('data-section');
        if (sectionId) {
            this.navigateToSection(sectionId);
        }
    }
    
    createClickEffect(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
    
    navigateToSection(sectionId) {
        console.log(`🧭 Navigating to admin section: ${sectionId}`);
        
        // Remove active class from all nav items and sections
        document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
            navItem.classList.remove('active');
        });
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Add active class to the corresponding section
        const section = document.getElementById(sectionId);
        if (section) {
            section.classList.add('active');
            
            // Update page title
            if (typeof updatePageTitle === 'function') {
                updatePageTitle(sectionId);
            }
            
            // Load section specific content
            this.loadSectionContent(sectionId);
            
            // Scroll to top smoothly
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }
    
    loadSectionContent(sectionId) {
        const loadingFunctions = {
            'generalSettings': 'loadGeneralSettingsContent',
            'paymentSettings': 'loadPaymentSettingsContent',
            'categories': 'loadCategoriesContent',
            'storeSettings': 'loadStoreSettingsContent',
            'securitySettings': 'loadSecuritySettingsContent'
        };
        
        const functionName = loadingFunctions[sectionId];
        if (functionName && typeof window[functionName] === 'function') {
            console.log(`📄 Loading content for: ${sectionId}`);
            window[functionName]();
        } else {
            console.log(`📄 Loading default content for: ${sectionId}`);
        }
    }
    
    checkCurrentSection() {
        setTimeout(() => {
            const currentSection = document.querySelector('.content-section.active')?.id;
            
            if (this.adminSettingsSections.includes(currentSection)) {
                this.expand();
                
                // Mark the appropriate submenu item as active
                const activeSubmenuItem = document.querySelector(`.admin-settings-submenu li[data-section="${currentSection}"]`);
                if (activeSubmenuItem) {
                    activeSubmenuItem.classList.add('active');
                }
            }
        }, 200);
    }
    
    setActiveSection(sectionId) {
        // Remove active class from all submenu items
        this.submenuItems.forEach(item => item.classList.remove('active'));
        
        // Add active class to the specified section
        const activeItem = document.querySelector(`.admin-settings-submenu li[data-section="${sectionId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
        
        // Expand menu if it's an admin settings section
        if (this.adminSettingsSections.includes(sectionId)) {
            this.expand();
        }
    }
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Global instance
let enhancedAdminSettingsMenu = null;

// Global functions for backward compatibility
function toggleAdminSettings() {
    if (enhancedAdminSettingsMenu) {
        enhancedAdminSettingsMenu.toggle();
    } else {
        console.warn('⚠️ Enhanced admin settings menu not initialized');
    }
}

function showAdminSection(sectionId) {
    if (enhancedAdminSettingsMenu) {
        enhancedAdminSettingsMenu.navigateToSection(sectionId);
    } else {
        console.warn('⚠️ Enhanced admin settings menu not initialized');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    enhancedAdminSettingsMenu = new EnhancedAdminSettingsMenu();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedAdminSettingsMenu;
}
