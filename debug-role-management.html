<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص إدارة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        #rolesManagementContent { border: 2px solid #ddd; border-radius: 10px; padding: 20px; margin-top: 20px; min-height: 300px; background: #f8f9fa; }
        .console-log { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; max-height: 200px; overflow-y: auto; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص إدارة الأدوار</h1>
        <p>فحص شامل لوظائف إدارة الأدوار وتشخيص المشاكل</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="runDiagnostics()">
                <i class="fas fa-play"></i> تشغيل التشخيص الشامل
            </button>
            <button class="test-button" onclick="testRoleManagementLoad()">
                <i class="fas fa-cog"></i> اختبار تحميل إدارة الأدوار
            </button>
            <button class="test-button" onclick="testAPIEndpoints()">
                <i class="fas fa-server"></i> اختبار نقاط النهاية
            </button>
            <button class="test-button" onclick="clearAll()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>

        <div id="testResults"></div>

        <div id="consoleOutput">
            <h3>📝 سجل وحدة التحكم:</h3>
            <div id="consoleLog" class="console-log"></div>
        </div>

        <!-- Container for role management content -->
        <div id="rolesManagementContent">
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>منطقة اختبار إدارة الأدوار</h3>
                <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
            </div>
        </div>
    </div>

    <!-- Load the users management script -->
    <script src="admin/js/users-management.js"></script>

    <script>
        // Capture console logs
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = [];
        
        function captureConsole(type, ...args) {
            const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
            consoleOutput.push(`[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`);
            updateConsoleDisplay();
        }
        
        console.log = function(...args) {
            captureConsole('log', ...args);
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            captureConsole('error', ...args);
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            captureConsole('warn', ...args);
            originalConsoleWarn.apply(console, args);
        };
        
        function updateConsoleDisplay() {
            const consoleLog = document.getElementById('consoleLog');
            consoleLog.innerHTML = consoleOutput.slice(-20).join('\n');
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAll() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleLog').innerHTML = '';
            consoleOutput.length = 0;
            document.getElementById('rolesManagementContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3>منطقة اختبار إدارة الأدوار</h3>
                    <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
                </div>
            `;
        }

        function runDiagnostics() {
            addResult('<h3>🔍 بدء التشخيص الشامل...</h3>');
            
            // Test 1: Check if script loaded
            addResult('<h4>1. فحص تحميل السكريبت:</h4>');
            if (typeof loadUsersManagementContent === 'function') {
                addResult('✅ تم تحميل ملف users-management.js بنجاح', 'success');
            } else {
                addResult('❌ فشل في تحميل ملف users-management.js', 'error');
                return;
            }
            
            // Test 2: Check role management functions
            addResult('<h4>2. فحص وظائف إدارة الأدوار:</h4>');
            const roleFunctions = [
                'loadRolesManagementContent',
                'showRolesManagement', 
                'loadRolesManagementInterface',
                'loadRolesData',
                'showAddRoleModal',
                'closeRoleModal',
                'saveRole',
                'editRole',
                'deleteRole'
            ];

            let functionsAvailable = 0;
            roleFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} - متوفرة`, 'success');
                    functionsAvailable++;
                } else {
                    addResult(`❌ ${funcName} - غير متوفرة`, 'error');
                }
            });
            
            addResult(`📊 الوظائف المتوفرة: ${functionsAvailable}/${roleFunctions.length}`, 'info');
            
            // Test 3: Check DOM elements
            addResult('<h4>3. فحص عناصر DOM:</h4>');
            const container = document.getElementById('rolesManagementContent');
            if (container) {
                addResult('✅ حاوي rolesManagementContent موجود', 'success');
            } else {
                addResult('❌ حاوي rolesManagementContent غير موجود', 'error');
            }
        }

        function testRoleManagementLoad() {
            addResult('<h3>🛡️ اختبار تحميل إدارة الأدوار:</h3>');
            
            try {
                if (typeof loadRolesManagementContent === 'function') {
                    addResult('✅ الوظيفة loadRolesManagementContent متوفرة', 'success');
                    addResult('🔄 محاولة تحميل المحتوى...', 'info');
                    
                    // Call the function
                    loadRolesManagementContent();
                    
                    // Check after a delay
                    setTimeout(() => {
                        const content = document.getElementById('rolesManagementContent').innerHTML;
                        console.log('Content after load:', content.substring(0, 200));
                        
                        if (content.includes('إدارة الأدوار والصلاحيات')) {
                            addResult('✅ تم تحميل محتوى إدارة الأدوار بنجاح!', 'success');
                        } else if (content.includes('جاري تحميل إدارة الأدوار')) {
                            addResult('🔄 المحتوى في حالة تحميل...', 'warning');
                            
                            // Check again after more time
                            setTimeout(() => {
                                const finalContent = document.getElementById('rolesManagementContent').innerHTML;
                                if (finalContent.includes('إدارة الأدوار والصلاحيات')) {
                                    addResult('✅ تم تحميل المحتوى بعد التأخير', 'success');
                                } else {
                                    addResult('❌ فشل في تحميل المحتوى حتى بعد التأخير', 'error');
                                }
                            }, 2000);
                        } else {
                            addResult('⚠️ تم استدعاء الوظيفة لكن المحتوى لم يتغير كما متوقع', 'warning');
                        }
                    }, 1000);
                } else {
                    addResult('❌ الوظيفة loadRolesManagementContent غير متوفرة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل إدارة الأدوار: ${error.message}`, 'error');
                console.error('Role management load error:', error);
            }
        }

        async function testAPIEndpoints() {
            addResult('<h3>🔗 اختبار نقاط النهاية للـ API:</h3>');
            
            const endpoints = [
                { url: 'php/api/roles.php?action=list', name: 'قائمة الأدوار' },
                { url: 'php/api/users.php?action=get_all', name: 'قائمة المستخدمين' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    addResult(`🔄 اختبار ${endpoint.name}...`, 'info');
                    
                    const response = await fetch(endpoint.url);
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        addResult(`✅ ${endpoint.name} - يعمل بشكل صحيح`, 'success');
                    } else {
                        addResult(`⚠️ ${endpoint.name} - استجابة غير متوقعة: ${data.message || 'غير محدد'}`, 'warning');
                    }
                } catch (error) {
                    addResult(`❌ ${endpoint.name} - خطأ في الاتصال: ${error.message}`, 'error');
                }
            }
        }

        // Auto-run diagnostics on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 تشخيص تلقائي عند تحميل الصفحة:</h3>');
                runDiagnostics();
            }, 500);
        });
    </script>
</body>
</html>
