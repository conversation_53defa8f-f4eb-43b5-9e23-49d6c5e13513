<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الفئات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .test-btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            color: white;
            background: #667eea;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #ffffff;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            border-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            border-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            border-color: #17a2b8;
            background: #d1ecf1;
            color: #0c5460;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9em;
        }
        
        #categoriesContainer {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-sitemap"></i> اختبار إدارة الفئات</h1>
        <p>اختبار شامل لوظائف إدارة الفئات</p>
        
        <!-- Test API -->
        <div class="test-section">
            <h3><i class="fas fa-code"></i> اختبار API</h3>
            <button class="test-btn" onclick="testAPI()">
                <i class="fas fa-play"></i> اختبار جلب الفئات
            </button>
            <button class="test-btn" onclick="testCreateCategory()">
                <i class="fas fa-plus"></i> اختبار إنشاء فئة
            </button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Test JavaScript -->
        <div class="test-section">
            <h3><i class="fas fa-code"></i> اختبار JavaScript</h3>
            <button class="test-btn" onclick="testJavaScript()">
                <i class="fas fa-play"></i> اختبار تحميل JavaScript
            </button>
            <button class="test-btn" onclick="loadCategoriesTest()">
                <i class="fas fa-download"></i> تحميل واجهة الفئات
            </button>
            <div id="jsResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Categories Container -->
        <div class="test-section">
            <h3><i class="fas fa-sitemap"></i> واجهة إدارة الفئات</h3>
            <div id="categoriesManagementContent">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Load JavaScript files -->
    <script src="js/categories-management-new.js"></script>
    
    <script>
        // Test API function
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار API...';
            
            try {
                const response = await fetch('php/categories.php?action=get_all');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4><i class="fas fa-check-circle"></i> نجح اختبار API</h4>
                        <p>تم جلب ${data.data.total} فئة بنجاح</p>
                        <details>
                            <summary>عرض البيانات الكاملة</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    throw new Error(data.message || 'فشل في جلب البيانات');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> فشل اختبار API</h4>
                    <p>خطأ: ${error.message}</p>
                `;
            }
        }
        
        // Test create category
        async function testCreateCategory() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار إنشاء فئة...';
            
            const testCategory = {
                name_ar: 'فئة اختبار',
                name_en: 'Test Category',
                slug: 'test-category-' + Date.now(),
                description_ar: 'هذه فئة للاختبار',
                icon: 'fas fa-test',
                color: '#ff6b6b',
                is_active: 1,
                is_featured: 0
            };
            
            try {
                const response = await fetch('php/categories.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'create',
                        ...testCategory
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4><i class="fas fa-check-circle"></i> نجح إنشاء الفئة</h4>
                        <p>تم إنشاء الفئة بنجاح - ID: ${data.data.id}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(data.message || 'فشل في إنشاء الفئة');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> فشل إنشاء الفئة</h4>
                    <p>خطأ: ${error.message}</p>
                `;
            }
        }
        
        // Test JavaScript loading
        function testJavaScript() {
            const resultDiv = document.getElementById('jsResult');
            resultDiv.style.display = 'block';
            
            const tests = [
                {
                    name: 'تحميل ملف JavaScript',
                    test: () => typeof loadCategoriesManagementContent === 'function'
                },
                {
                    name: 'وجود دالة fetchCategories',
                    test: () => typeof fetchCategories === 'function'
                },
                {
                    name: 'وجود دالة renderCategoriesManagement',
                    test: () => typeof renderCategoriesManagement === 'function'
                },
                {
                    name: 'وجود دالة showAddCategoryModal',
                    test: () => typeof showAddCategoryModal === 'function'
                }
            ];
            
            let results = '<h4><i class="fas fa-code"></i> نتائج اختبار JavaScript</h4>';
            let allPassed = true;
            
            tests.forEach(test => {
                const passed = test.test();
                allPassed = allPassed && passed;
                const icon = passed ? '<i class="fas fa-check" style="color: green;"></i>' : '<i class="fas fa-times" style="color: red;"></i>';
                results += `<p>${icon} ${test.name}: ${passed ? 'نجح' : 'فشل'}</p>`;
            });
            
            resultDiv.className = allPassed ? 'result success' : 'result error';
            resultDiv.innerHTML = results;
        }
        
        // Test loading categories interface
        function loadCategoriesTest() {
            const resultDiv = document.getElementById('jsResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تحميل واجهة الفئات...';
            
            try {
                if (typeof loadCategoriesManagementContent === 'function') {
                    loadCategoriesManagementContent();
                    
                    // Check after a delay
                    setTimeout(() => {
                        const container = document.getElementById('categoriesManagementContent');
                        if (container && container.innerHTML.trim() !== '') {
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = '<h4><i class="fas fa-check-circle"></i> نجح تحميل واجهة الفئات</h4><p>تم تحميل المحتوى بنجاح</p>';
                        } else {
                            resultDiv.className = 'result error';
                            resultDiv.innerHTML = '<h4><i class="fas fa-exclamation-triangle"></i> فشل تحميل واجهة الفئات</h4><p>لم يتم تحميل المحتوى</p>';
                        }
                    }, 3000);
                } else {
                    throw new Error('دالة loadCategoriesManagementContent غير موجودة');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل الواجهة</h4>
                    <p>خطأ: ${error.message}</p>
                `;
            }
        }
        
        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 بدء الاختبارات التلقائية...');
            testJavaScript();
        });
    </script>
</body>
</html>
