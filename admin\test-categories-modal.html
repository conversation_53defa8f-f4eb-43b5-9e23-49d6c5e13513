<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج الفئات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        #categoryModal {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نموذج إدارة الفئات</h1>
        
        <div class="test-section">
            <h3>اختبار النماذج</h3>
            <button class="btn-primary" onclick="testAddModal()">اختبار نموذج الإضافة</button>
            <button class="btn-warning" onclick="testEditModal()">اختبار نموذج التعديل</button>
            <button class="btn-success" onclick="loadTestData()">تحميل بيانات تجريبية</button>
        </div>
        
        <div class="test-section">
            <h3>اختبار API</h3>
            <button class="btn-primary" onclick="testCreateCategory()">إنشاء فئة تجريبية</button>
            <button class="btn-warning" onclick="testGetCategories()">جلب الفئات</button>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4>النتيجة:</h4>
            <pre id="resultContent"></pre>
        </div>
        
        <!-- Modal Container -->
        <div id="categoryModal"></div>
    </div>

    <script src="js/categories-interactive.js"></script>
    <script>
        // Test data
        const testCategories = [
            {
                id: 1,
                name_ar: 'الكتب',
                name_en: 'Books',
                description_ar: 'فئة الكتب الرئيسية',
                parent_id: null,
                color: '#667eea',
                icon: 'fas fa-book',
                is_active: 1,
                is_featured: 1,
                sort_order: 1
            },
            {
                id: 2,
                name_ar: 'الروايات',
                name_en: 'Novels',
                description_ar: 'روايات وقصص',
                parent_id: 1,
                color: '#28a745',
                icon: 'fas fa-book-open',
                is_active: 1,
                is_featured: 0,
                sort_order: 1
            }
        ];

        function showResult(data) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            resultContent.textContent = JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        function loadTestData() {
            // Simulate loading categories data
            window.allCategoriesData = testCategories;
            window.mainCategoriesData = testCategories.filter(c => c.parent_id === null);
            
            console.log('📦 تم تحميل البيانات التجريبية:', {
                all: window.allCategoriesData,
                main: window.mainCategoriesData
            });
            
            showResult({
                message: 'تم تحميل البيانات التجريبية',
                allCategories: window.allCategoriesData.length,
                mainCategories: window.mainCategoriesData.length
            });
        }

        function testAddModal() {
            console.log('🧪 اختبار نموذج الإضافة...');
            if (typeof showAddCategoryModal === 'function') {
                showAddCategoryModal();
                showResult({ message: 'تم فتح نموذج الإضافة بنجاح' });
            } else {
                showResult({ error: 'دالة showAddCategoryModal غير متاحة' });
            }
        }

        function testEditModal() {
            console.log('🧪 اختبار نموذج التعديل...');
            loadTestData(); // Ensure test data is loaded
            
            if (typeof editCategory === 'function') {
                // Simulate editing the first category
                const testCategory = testCategories[0];
                editingCategoryId = testCategory.id;
                
                const modalContainer = document.getElementById('categoryModal');
                modalContainer.innerHTML = createCategoryModal(true, testCategory);
                modalContainer.style.display = 'block';
                
                showResult({ message: 'تم فتح نموذج التعديل بنجاح', category: testCategory });
            } else {
                showResult({ error: 'دالة editCategory غير متاحة' });
            }
        }

        async function testCreateCategory() {
            const testData = {
                name_ar: 'فئة تجريبية ' + Date.now(),
                name_en: 'Test Category ' + Date.now(),
                description_ar: 'وصف الفئة التجريبية',
                color: '#667eea',
                icon: 'fas fa-test',
                is_active: 1,
                is_featured: 0,
                sort_order: 0
            };

            try {
                const response = await fetch('php/categories.php?action=create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: error.message });
            }
        }

        async function testGetCategories() {
            try {
                const response = await fetch('php/categories.php?action=get_all');
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: error.message });
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 صفحة اختبار النماذج جاهزة');
            loadTestData();
        });
    </script>
</body>
</html>
