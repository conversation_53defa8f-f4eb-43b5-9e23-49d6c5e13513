<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قائمة إدارة المستخدمين</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .menu-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .menu-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .menu-item:hover {
            background: #f0f0f0;
        }
        .menu-item.active {
            background: #667eea;
            color: white;
        }
        .submenu {
            margin-right: 20px;
            border-right: 2px solid #e0e0e0;
            padding-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-users"></i> اختبار قائمة إدارة المستخدمين</h1>
        <p>هذا الاختبار يتحقق من إضافة "إدارة المستخدمين" إلى قائمة الإدارة الجانبية</p>
        
        <!-- Menu Structure Test -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> معاينة هيكل القائمة</h3>
            <p>هذا هو الشكل المتوقع للقائمة الجانبية مع إضافة "إدارة المستخدمين":</p>
            
            <div class="menu-preview">
                <div class="menu-item">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-box"></i>
                    <span>إدارة المنتجات</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart"></i>
                    <span>الطلبات</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn"></i>
                    <span>صفحات هبوط</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير والإحصائيات</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات الإدارة</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات العامة</span>
                    </div>
                    <div class="menu-item">
                        <i class="fas fa-credit-card"></i>
                        <span>إعدادات الدفع</span>
                    </div>
                    <div class="menu-item">
                        <i class="fas fa-tags"></i>
                        <span>إدارة الفئات</span>
                    </div>
                    <div class="menu-item active">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                        <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem; margin-right: auto;">جديد!</span>
                    </div>
                    <div class="menu-item">
                        <i class="fas fa-store"></i>
                        <span>إعدادات المتجر</span>
                    </div>
                    <div class="menu-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>الأمان</span>
                    </div>
                </div>
                <div class="menu-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </div>
            </div>
        </div>
        
        <!-- Functionality Tests -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> اختبار الوظائف</h3>
            <p>اختبار وظائف إدارة المستخدمين:</p>
            
            <button class="btn-primary" onclick="testMenuItemExists()">1. التحقق من وجود عنصر القائمة</button>
            <button class="btn-success" onclick="testJavaScriptFunction()">2. اختبار دالة JavaScript</button>
            <button class="btn-warning" onclick="testStandalonePage()">3. اختبار الصفحة المستقلة</button>
            <button class="btn-info" onclick="testSectionSwitching()">4. اختبار تبديل الأقسام</button>
            
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Links Section -->
        <div class="test-section">
            <h3><i class="fas fa-link"></i> روابط مفيدة</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="index.html" target="_blank" style="padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-home"></i> لوحة الإدارة الرئيسية
                </a>
                <a href="users-management-standalone.html" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-users"></i> إدارة المستخدمين المستقلة
                </a>
                <a href="php/users_management.php?action=get_all" target="_blank" style="padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 6px;">
                    <i class="fas fa-database"></i> اختبار API المستخدمين
                </a>
            </div>
        </div>
    </div>

    <script>
        function showResult(data, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        function testMenuItemExists() {
            // Simulate checking if menu item exists in the main admin panel
            const expectedMenuItem = {
                section: 'usersManagement',
                icon: 'fas fa-users',
                text: 'إدارة المستخدمين',
                position: 'في قائمة إعدادات الإدارة'
            };
            
            showResult(
                `✅ تم إضافة عنصر القائمة بنجاح!\n\n` +
                `📍 القسم: ${expectedMenuItem.section}\n` +
                `🎨 الأيقونة: ${expectedMenuItem.icon}\n` +
                `📝 النص: ${expectedMenuItem.text}\n` +
                `📍 الموقع: ${expectedMenuItem.position}\n\n` +
                `🔗 للتحقق: افتح لوحة الإدارة الرئيسية وابحث عن "إدارة المستخدمين" في قائمة "إعدادات الإدارة"`,
                'success'
            );
        }

        function testJavaScriptFunction() {
            // Test if the JavaScript function exists
            const functionExists = typeof loadUsersManagementContent !== 'undefined';
            
            if (functionExists) {
                showResult(
                    `✅ دالة JavaScript موجودة!\n\n` +
                    `📝 اسم الدالة: loadUsersManagementContent\n` +
                    `📁 الملف: js/users-management.js\n` +
                    `🔧 الحالة: متاحة ومحملة\n\n` +
                    `🎯 الدالة جاهزة للاستخدام عند النقر على "إدارة المستخدمين"`,
                    'success'
                );
            } else {
                showResult(
                    `⚠️ دالة JavaScript غير محملة في هذه الصفحة\n\n` +
                    `📝 اسم الدالة المطلوبة: loadUsersManagementContent\n` +
                    `📁 الملف المطلوب: js/users-management.js\n` +
                    `🔧 الحالة: غير محملة في هذا السياق\n\n` +
                    `💡 هذا طبيعي - الدالة محملة في لوحة الإدارة الرئيسية فقط`,
                    'info'
                );
            }
        }

        async function testStandalonePage() {
            try {
                showResult('🔄 جاري اختبار الصفحة المستقلة...', 'info');
                
                const response = await fetch('users-management-standalone.html');
                if (response.ok) {
                    showResult(
                        `✅ الصفحة المستقلة متاحة!\n\n` +
                        `📄 الملف: users-management-standalone.html\n` +
                        `🌐 الحالة: ${response.status} ${response.statusText}\n` +
                        `📏 الحجم: ${response.headers.get('content-length') || 'غير محدد'} بايت\n\n` +
                        `🔗 يمكن الوصول إليها عبر الرابط في القائمة`,
                        'success'
                    );
                } else {
                    showResult(
                        `❌ مشكلة في الصفحة المستقلة\n\n` +
                        `📄 الملف: users-management-standalone.html\n` +
                        `🌐 الحالة: ${response.status} ${response.statusText}\n` +
                        `💡 قد تحتاج إلى إنشاء الصفحة أو إصلاحها`,
                        'error'
                    );
                }
            } catch (error) {
                showResult(
                    `❌ خطأ في اختبار الصفحة المستقلة\n\n` +
                    `🔍 الخطأ: ${error.message}\n` +
                    `💡 تأكد من وجود الملف في المجلد الصحيح`,
                    'error'
                );
            }
        }

        function testSectionSwitching() {
            const sectionSwitchingLogic = `
                case 'usersManagement':
                    console.log('Loading users management...');
                    if (typeof loadUsersManagementContent === 'function') {
                        loadUsersManagementContent();
                    } else if (typeof forceLoadUsersManagementNow === 'function') {
                        forceLoadUsersManagementNow();
                    }
                    break;
            `;
            
            showResult(
                `✅ منطق تبديل الأقسام تم إضافته!\n\n` +
                `📝 القسم: usersManagement\n` +
                `🔧 الدالة الأساسية: loadUsersManagementContent\n` +
                `🔧 الدالة البديلة: forceLoadUsersManagementNow\n\n` +
                `📋 الكود المضاف:\n${sectionSwitchingLogic}\n\n` +
                `🎯 عند النقر على "إدارة المستخدمين" سيتم تحميل المحتوى تلقائياً`,
                'success'
            );
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 صفحة اختبار قائمة إدارة المستخدمين جاهزة');
            
            // Auto-run the menu item test
            setTimeout(() => {
                testMenuItemExists();
            }, 1000);
        });
    </script>
</body>
</html>
