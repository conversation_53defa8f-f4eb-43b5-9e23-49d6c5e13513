<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - قائمة إعدادات الإدارة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 600px;
        }
        
        .test-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 0;
        }
        
        .test-main {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .status-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .test-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        
        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-btn.danger {
            background: #dc3545;
            color: white;
        }
        
        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success {
            color: #68d391;
        }
        
        .log-entry.error {
            color: #fc8181;
        }
        
        .log-entry.info {
            color: #63b3ed;
        }
        
        @media (max-width: 768px) {
            .test-content {
                grid-template-columns: 1fr;
            }
            
            .test-sidebar {
                order: 2;
            }
            
            .test-main {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> اختبار شامل - قائمة إعدادات الإدارة</h1>
            <p>اختبار جميع وظائف القائمة القابلة للطي والتنقل</p>
        </div>
        
        <div class="test-content">
            <div class="test-sidebar">
                <!-- This will be loaded from the main admin page -->
                <div id="sidebarContent">
                    <div style="text-align: center; padding: 40px; color: white;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                        <p>جاري تحميل الشريط الجانبي...</p>
                    </div>
                </div>
            </div>
            
            <div class="test-main">
                <div class="status-panel">
                    <h3><i class="fas fa-chart-line"></i> حالة الاختبارات</h3>
                    
                    <div class="status-item">
                        <span>تحميل القائمة</span>
                        <span id="loadStatus" class="status-badge status-warning">جاري التحميل...</span>
                    </div>
                    
                    <div class="status-item">
                        <span>وظيفة التوسع/الانكماش</span>
                        <span id="toggleStatus" class="status-badge status-warning">لم يتم الاختبار</span>
                    </div>
                    
                    <div class="status-item">
                        <span>التنقل بين الأقسام</span>
                        <span id="navigationStatus" class="status-badge status-warning">لم يتم الاختبار</span>
                    </div>
                    
                    <div class="status-item">
                        <span>التمييز النشط</span>
                        <span id="activeStatus" class="status-badge status-warning">لم يتم الاختبار</span>
                    </div>
                </div>
                
                <div class="status-panel">
                    <h3><i class="fas fa-play-circle"></i> اختبارات تفاعلية</h3>
                    
                    <div class="test-buttons">
                        <button class="test-btn primary" onclick="testMenuLoad()">
                            <i class="fas fa-download"></i>
                            اختبار التحميل
                        </button>
                        
                        <button class="test-btn success" onclick="testToggleFunction()">
                            <i class="fas fa-expand-arrows-alt"></i>
                            اختبار التوسع/الانكماش
                        </button>
                        
                        <button class="test-btn warning" onclick="testNavigation()">
                            <i class="fas fa-route"></i>
                            اختبار التنقل
                        </button>
                        
                        <button class="test-btn danger" onclick="clearLog()">
                            <i class="fas fa-trash"></i>
                            مسح السجل
                        </button>
                    </div>
                </div>
                
                <div class="status-panel">
                    <h3><i class="fas fa-terminal"></i> سجل الأحداث</h3>
                    <div id="logPanel" class="log-panel">
                        <div class="log-entry info">[INFO] بدء اختبار قائمة إعدادات الإدارة...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        let testResults = {
            load: false,
            toggle: false,
            navigation: false,
            active: false
        };
        
        function log(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(entry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        function updateStatus(testName, status) {
            const statusElement = document.getElementById(testName + 'Status');
            if (statusElement) {
                statusElement.className = `status-badge status-${status}`;
                statusElement.textContent = status === 'success' ? 'نجح' : status === 'error' ? 'فشل' : 'جاري الاختبار...';
            }
            testResults[testName] = status === 'success';
        }
        
        function testMenuLoad() {
            log('بدء اختبار تحميل القائمة...', 'info');
            
            try {
                // Load sidebar content from main admin page
                fetch('/admin/index.html')
                    .then(response => response.text())
                    .then(html => {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const sidebar = doc.querySelector('.sidebar');
                        
                        if (sidebar) {
                            document.getElementById('sidebarContent').innerHTML = sidebar.innerHTML;
                            log('تم تحميل الشريط الجانبي بنجاح', 'success');
                            updateStatus('load', 'success');
                            
                            // Load CSS
                            const link = document.createElement('link');
                            link.rel = 'stylesheet';
                            link.href = '/admin/css/admin.css';
                            document.head.appendChild(link);
                            
                            // Load JS
                            const script = document.createElement('script');
                            script.src = '/admin/js/admin-settings-menu.js';
                            document.head.appendChild(script);
                            
                            log('تم تحميل الملفات المطلوبة', 'success');
                        } else {
                            throw new Error('لم يتم العثور على الشريط الجانبي');
                        }
                    })
                    .catch(error => {
                        log(`خطأ في تحميل القائمة: ${error.message}`, 'error');
                        updateStatus('load', 'error');
                    });
            } catch (error) {
                log(`خطأ في اختبار التحميل: ${error.message}`, 'error');
                updateStatus('load', 'error');
            }
        }
        
        function testToggleFunction() {
            log('بدء اختبار وظيفة التوسع/الانكماش...', 'info');
            
            try {
                if (typeof toggleAdminSettings === 'function') {
                    toggleAdminSettings();
                    log('تم استدعاء دالة toggleAdminSettings بنجاح', 'success');
                    updateStatus('toggle', 'success');
                } else {
                    throw new Error('دالة toggleAdminSettings غير موجودة');
                }
            } catch (error) {
                log(`خطأ في اختبار التوسع/الانكماش: ${error.message}`, 'error');
                updateStatus('toggle', 'error');
            }
        }
        
        function testNavigation() {
            log('بدء اختبار التنقل بين الأقسام...', 'info');
            
            const sections = ['generalSettings', 'paymentSettings', 'categories', 'storeSettings', 'securitySettings'];
            let testCount = 0;
            
            sections.forEach((section, index) => {
                setTimeout(() => {
                    try {
                        if (typeof showAdminSection === 'function') {
                            showAdminSection(section);
                            log(`تم التنقل إلى قسم: ${section}`, 'success');
                            testCount++;
                            
                            if (testCount === sections.length) {
                                updateStatus('navigation', 'success');
                                updateStatus('active', 'success');
                                log('تم اختبار جميع أقسام التنقل بنجاح', 'success');
                            }
                        } else {
                            throw new Error('دالة showAdminSection غير موجودة');
                        }
                    } catch (error) {
                        log(`خطأ في التنقل إلى ${section}: ${error.message}`, 'error');
                        updateStatus('navigation', 'error');
                    }
                }, index * 1000);
            });
        }
        
        function clearLog() {
            document.getElementById('logPanel').innerHTML = '<div class="log-entry info">[INFO] تم مسح السجل</div>';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار', 'success');
            
            // Auto-load menu after 1 second
            setTimeout(testMenuLoad, 1000);
        });
    </script>
</body>
</html>
