<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل في لوحة التحكم</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        .menu-item { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin: 5px 0; display: flex; justify-content: space-between; align-items: center; }
        .menu-item.visible { border-color: #28a745; background: #d4edda; }
        .menu-item.hidden { border-color: #dc3545; background: #f8d7da; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧭 اختبار التنقل في لوحة التحكم</h1>
        <p>فحص عناصر القائمة في لوحة التحكم والتأكد من ظهورها بشكل صحيح</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="testMenuVisibility()">
                <i class="fas fa-eye"></i> فحص ظهور عناصر القائمة
            </button>
            <button class="test-button" onclick="testMenuFunctionality()">
                <i class="fas fa-cog"></i> اختبار وظائف القائمة
            </button>
            <button class="test-button" onclick="testAdminSettingsExpansion()">
                <i class="fas fa-expand"></i> اختبار توسيع إعدادات الإدارة
            </button>
            <button class="test-button" onclick="openAdminPanel()">
                <i class="fas fa-external-link-alt"></i> فتح لوحة التحكم
            </button>
            <button class="test-button" onclick="clearAll()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>

        <div id="testResults"></div>

        <div id="menuItemsStatus" style="margin-top: 20px;">
            <h3>📋 حالة عناصر القائمة:</h3>
            <div id="menuItemsList"></div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAll() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('menuItemsList').innerHTML = '';
        }

        function openAdminPanel() {
            window.open('admin/index.html', '_blank');
        }

        function testMenuVisibility() {
            addResult('<h3>👁️ فحص ظهور عناصر القائمة:</h3>');
            
            // Expected menu items in admin settings
            const expectedMenuItems = [
                { id: 'generalSettings', name: 'الإعدادات العامة', icon: 'fas fa-cog' },
                { id: 'paymentSettings', name: 'إعدادات الدفع', icon: 'fas fa-credit-card' },
                { id: 'categories', name: 'إدارة الفئات', icon: 'fas fa-tags' },
                { id: 'usersManagement', name: 'إدارة المستخدمين', icon: 'fas fa-users' },
                { id: 'rolesManagement', name: 'إدارة الأدوار', icon: 'fas fa-user-shield' },
                { id: 'storeSettings', name: 'إعدادات المتجر', icon: 'fas fa-store' },
                { id: 'storesManagement', name: 'إدارة المتاجر', icon: 'fas fa-store-alt' },
                { id: 'securitySettings', name: 'الأمان', icon: 'fas fa-shield-alt' },
                { id: 'subscriptionsManagement', name: 'إدارة الاشتراكات', icon: 'fas fa-crown' }
            ];

            // Test by trying to fetch the admin panel HTML
            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    let foundItems = 0;
                    let missingItems = [];
                    let menuItemsHtml = '';

                    expectedMenuItems.forEach(item => {
                        const menuElement = doc.querySelector(`li[data-section="${item.id}"]`);
                        if (menuElement) {
                            foundItems++;
                            addResult(`✅ ${item.name} (${item.id}) - موجود`, 'success');
                            menuItemsHtml += `<div class="menu-item visible">
                                <span><i class="${item.icon}"></i> ${item.name}</span>
                                <span style="color: #28a745;">✅ موجود</span>
                            </div>`;
                        } else {
                            missingItems.push(item);
                            addResult(`❌ ${item.name} (${item.id}) - مفقود`, 'error');
                            menuItemsHtml += `<div class="menu-item hidden">
                                <span><i class="${item.icon}"></i> ${item.name}</span>
                                <span style="color: #dc3545;">❌ مفقود</span>
                            </div>`;
                        }
                    });

                    document.getElementById('menuItemsList').innerHTML = menuItemsHtml;

                    addResult(`📊 النتيجة: ${foundItems}/${expectedMenuItems.length} عنصر موجود`, 'info');
                    
                    if (missingItems.length === 0) {
                        addResult('🎉 جميع عناصر القائمة موجودة!', 'success');
                    } else {
                        addResult(`⚠️ ${missingItems.length} عنصر مفقود`, 'warning');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في جلب لوحة التحكم: ${error.message}`, 'error');
                });
        }

        function testMenuFunctionality() {
            addResult('<h3>⚙️ اختبار وظائف القائمة:</h3>');
            
            // Test JavaScript functions
            const functionsToTest = [
                'loadSecuritySettingsContent',
                'loadSubscriptionsManagementContent',
                'toggleAdminSettings',
                'showAdminSection'
            ];

            // Since we can't directly test functions from another page, we'll check if the scripts are loaded
            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    // Check for script inclusions
                    if (html.includes('security-settings.js')) {
                        addResult('✅ سكريبت إعدادات الأمان مُحمل', 'success');
                    } else {
                        addResult('❌ سكريبت إعدادات الأمان غير مُحمل', 'error');
                    }

                    if (html.includes('subscriptions-management.js')) {
                        addResult('✅ سكريبت إدارة الاشتراكات مُحمل', 'success');
                    } else {
                        addResult('❌ سكريبت إدارة الاشتراكات غير مُحمل', 'error');
                    }

                    if (html.includes('toggleAdminSettings')) {
                        addResult('✅ وظيفة toggleAdminSettings موجودة', 'success');
                    } else {
                        addResult('❌ وظيفة toggleAdminSettings مفقودة', 'error');
                    }

                    if (html.includes('admin-settings-menu-enhanced.js')) {
                        addResult('✅ سكريبت القائمة المحسن مُحمل', 'success');
                    } else {
                        addResult('❌ سكريبت القائمة المحسن غير مُحمل', 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في فحص الوظائف: ${error.message}`, 'error');
                });
        }

        function testAdminSettingsExpansion() {
            addResult('<h3>🔍 اختبار توسيع إعدادات الإدارة:</h3>');
            
            fetch('admin/index.html')
                .then(response => response.text())
                .then(html => {
                    // Check for expansion logic
                    if (html.includes('admin-settings-menu expanded')) {
                        addResult('✅ منطق توسيع القائمة موجود', 'success');
                    }

                    if (html.includes('DOMContentLoaded') && html.includes('expanded')) {
                        addResult('✅ توسيع تلقائي عند تحميل الصفحة', 'success');
                    } else {
                        addResult('⚠️ قد لا يتم توسيع القائمة تلقائياً', 'warning');
                    }

                    if (html.includes('admin-settings-arrow')) {
                        addResult('✅ سهم التوسيع موجود', 'success');
                    } else {
                        addResult('❌ سهم التوسيع مفقود', 'error');
                    }

                    if (html.includes('max-height') || html.includes('maxHeight')) {
                        addResult('✅ آلية التوسيع بـ max-height موجودة', 'success');
                    } else {
                        addResult('❌ آلية التوسيع مفقودة', 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في فحص التوسيع: ${error.message}`, 'error');
                });
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('<h3>🔄 اختبار تلقائي عند تحميل الصفحة:</h3>');
                testMenuVisibility();
            }, 500);
        });
    </script>
</body>
</html>
