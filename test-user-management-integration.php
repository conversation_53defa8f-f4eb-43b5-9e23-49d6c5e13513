<?php
/**
 * Test User Management Integration
 * اختبار تكامل نظام إدارة المستخدمين
 */

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل إدارة المستخدمين</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        h1, h2, h3 { color: #333; }
        .test-button { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-button:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تكامل نظام إدارة المستخدمين</h1>
        <p>اختبار شامل للتأكد من عمل نظام إدارة المستخدمين مع قاعدة البيانات الموحدة</p>

        <?php
        try {
            $pdo = getPDOConnection();
            
            echo "<div class='section success'>";
            echo "<h2>✅ 1. اختبار الاتصال بقاعدة البيانات</h2>";
            echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
            echo "</div>";
            
            // Test users table structure
            echo "<div class='section'>";
            echo "<h2>📋 2. اختبار هيكل جدول المستخدمين</h2>";
            
            $stmt = $pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>القيمة الافتراضية</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // Test user_roles table
            echo "<div class='section'>";
            echo "<h2>🛡️ 3. اختبار جدول الأدوار</h2>";
            
            $stmt = $pdo->query("SELECT * FROM user_roles ORDER BY level DESC");
            $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($roles) {
                echo "<table>";
                echo "<tr><th>ID</th><th>الاسم</th><th>الاسم العربي</th><th>المستوى</th><th>الحالة</th></tr>";
                foreach ($roles as $role) {
                    echo "<tr>";
                    echo "<td>" . $role['id'] . "</td>";
                    echo "<td>" . $role['name'] . "</td>";
                    echo "<td>" . $role['display_name_ar'] . "</td>";
                    echo "<td>" . $role['level'] . "</td>";
                    echo "<td>" . ($role['is_active'] ? 'نشط' : 'غير نشط') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='warning'>⚠️ لا توجد أدوار في النظام</div>";
            }
            echo "</div>";
            
            // Test users with roles
            echo "<div class='section'>";
            echo "<h2>👥 4. اختبار المستخدمين مع الأدوار</h2>";
            
            $stmt = $pdo->query("
                SELECT u.id, u.username, u.email, u.first_name, u.last_name, 
                       ur.display_name_ar as role_name, u.status, u.created_at
                FROM users u 
                LEFT JOIN user_roles ur ON u.role_id = ur.id 
                ORDER BY u.created_at DESC 
                LIMIT 10
            ");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($users) {
                echo "<table>";
                echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>" . $user['id'] . "</td>";
                    echo "<td>" . $user['username'] . "</td>";
                    echo "<td>" . $user['first_name'] . ' ' . $user['last_name'] . "</td>";
                    echo "<td>" . $user['email'] . "</td>";
                    echo "<td>" . ($user['role_name'] ?? 'غير محدد') . "</td>";
                    echo "<td>" . $user['status'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='warning'>⚠️ لا يوجد مستخدمين في النظام</div>";
            }
            echo "</div>";
            
            // Test API endpoints
            echo "<div class='section'>";
            echo "<h2>🔗 5. اختبار نقاط النهاية للـ API</h2>";
            
            $apiEndpoints = [
                'php/api/users.php?action=list' => 'قائمة المستخدمين',
                'php/api/roles.php?action=list' => 'قائمة الأدوار',
                'api/users.php' => 'API المستخدمين القديم'
            ];
            
            foreach ($apiEndpoints as $endpoint => $description) {
                if (file_exists($endpoint)) {
                    echo "<div class='success'>✅ $description - الملف موجود</div>";
                } else {
                    echo "<div class='error'>❌ $description - الملف غير موجود: $endpoint</div>";
                }
            }
            echo "</div>";
            
            // Test navigation integration
            echo "<div class='section'>";
            echo "<h2>🧭 6. اختبار تكامل التنقل</h2>";
            
            $adminIndexPath = 'admin/index.html';
            if (file_exists($adminIndexPath)) {
                $content = file_get_contents($adminIndexPath);
                
                if (strpos($content, 'data-section="rolesManagement"') !== false) {
                    echo "<div class='success'>✅ قسم إدارة الأدوار موجود في التنقل</div>";
                } else {
                    echo "<div class='error'>❌ قسم إدارة الأدوار غير موجود في التنقل</div>";
                }
                
                if (strpos($content, 'case \'rolesManagement\'') !== false) {
                    echo "<div class='success'>✅ معالج JavaScript لإدارة الأدوار موجود</div>";
                } else {
                    echo "<div class='error'>❌ معالج JavaScript لإدارة الأدوار غير موجود</div>";
                }
            } else {
                echo "<div class='error'>❌ ملف لوحة التحكم غير موجود</div>";
            }
            echo "</div>";
            
            echo "<div class='section success'>";
            echo "<h2>🎉 النتيجة النهائية</h2>";
            echo "<p>تم اختبار جميع مكونات نظام إدارة المستخدمين بنجاح!</p>";
            echo "<a href='admin/index.html' class='test-button'>🚀 فتح لوحة التحكم</a>";
            echo "<a href='admin/users-management-showcase.html' class='test-button'>👥 عرض إدارة المستخدمين</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h2>❌ خطأ في الاختبار</h2>";
            echo "<p>حدث خطأ أثناء اختبار النظام: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
