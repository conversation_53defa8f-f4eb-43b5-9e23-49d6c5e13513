<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحلول المُصححة</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-bug-slash"></i> اختبار الحلول المُصححة</h1>
            <p>فحص شامل للمشاكل التي تم إصلاحها في النظام</p>
        </div>

        <!-- API Endpoints Test -->
        <div class="test-section">
            <h3><i class="fas fa-plug"></i> اختبار نقاط API</h3>
            <div id="api-tests">
                <div class="test-item">
                    <span>API المستخدمين (users.php)</span>
                    <span class="status" id="users-api-status">جاري الفحص...</span>
                </div>
                <div class="test-item">
                    <span>API العمليات المجمعة (users-bulk.php)</span>
                    <span class="status" id="bulk-api-status">جاري الفحص...</span>
                </div>
                <div class="test-item">
                    <span>API تصدير المستخدمين (users-export.php)</span>
                    <span class="status" id="export-api-status">جاري الفحص...</span>
                </div>
                <div class="test-item">
                    <span>API الإعدادات العامة (general-settings.php)</span>
                    <span class="status" id="settings-api-status">جاري الفحص...</span>
                </div>
            </div>
        </div>

        <!-- JavaScript Files Test -->
        <div class="test-section">
            <h3><i class="fas fa-code"></i> اختبار ملفات JavaScript</h3>
            <div id="js-tests">
                <div class="test-item">
                    <span>مدير الإشعارات (notification-manager.js)</span>
                    <span class="status" id="notification-js-status">جاري الفحص...</span>
                </div>
                <div class="test-item">
                    <span>إدارة المستخدمين (user-management.js)</span>
                    <span class="status" id="user-js-status">جاري الفحص...</span>
                </div>
                <div class="test-item">
                    <span>الإعدادات العامة (general-settings.js)</span>
                    <span class="status" id="settings-js-status">جاري الفحص...</span>
                </div>
            </div>
        </div>

        <!-- Functionality Test -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار الوظائف</h3>
            <div id="functionality-tests">
                <div class="test-item">
                    <span>مدير الإشعارات</span>
                    <button class="btn btn-primary" onclick="testNotifications()">اختبار الإشعارات</button>
                </div>
                <div class="test-item">
                    <span>تحميل بيانات المستخدمين</span>
                    <button class="btn btn-success" onclick="testUserLoading()">اختبار التحميل</button>
                </div>
                <div class="test-item">
                    <span>حفظ الإعدادات العامة</span>
                    <button class="btn btn-warning" onclick="testSettingsSave()">اختبار الحفظ</button>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="test-section">
            <h3><i class="fas fa-external-link-alt"></i> روابط سريعة للاختبار</h3>
            <div style="text-align: center;">
                <a href="admin/user-management.html" class="btn btn-primary" target="_blank">
                    <i class="fas fa-users"></i> إدارة المستخدمين
                </a>
                <a href="admin/general-settings.html" class="btn btn-success" target="_blank">
                    <i class="fas fa-cogs"></i> الإعدادات العامة
                </a>
                <a href="api/users.php" class="btn btn-warning" target="_blank">
                    <i class="fas fa-database"></i> API المستخدمين
                </a>
            </div>
        </div>

        <div class="test-results" id="test-results">
            <h4><i class="fas fa-chart-bar"></i> نتائج الاختبار</h4>
            <p id="results-summary">جاري تشغيل الاختبارات...</p>
        </div>
    </div>

    <script>
        // Test API endpoints
        async function testAPIEndpoints() {
            const endpoints = [
                { id: 'users-api-status', url: 'api/users.php', name: 'Users API' },
                { id: 'bulk-api-status', url: 'api/users-bulk.php', name: 'Bulk API' },
                { id: 'export-api-status', url: 'api/users-export.php', name: 'Export API' },
                { id: 'settings-api-status', url: 'api/general-settings.php', name: 'Settings API' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    const statusElement = document.getElementById(endpoint.id);
                    
                    if (response.ok || response.status === 405) { // 405 = Method Not Allowed is OK for GET on POST endpoints
                        statusElement.textContent = 'متاح';
                        statusElement.className = 'status success';
                    } else {
                        statusElement.textContent = `خطأ ${response.status}`;
                        statusElement.className = 'status error';
                    }
                } catch (error) {
                    const statusElement = document.getElementById(endpoint.id);
                    statusElement.textContent = 'غير متاح';
                    statusElement.className = 'status error';
                }
            }
        }

        // Test JavaScript files
        function testJavaScriptFiles() {
            const jsTests = [
                { id: 'notification-js-status', obj: 'notificationManager', name: 'Notification Manager' },
                { id: 'user-js-status', obj: 'userManagement', name: 'User Management' },
                { id: 'settings-js-status', obj: 'generalSettings', name: 'General Settings' }
            ];

            jsTests.forEach(test => {
                const statusElement = document.getElementById(test.id);
                if (typeof window[test.obj] !== 'undefined') {
                    statusElement.textContent = 'محمل';
                    statusElement.className = 'status success';
                } else {
                    statusElement.textContent = 'غير محمل';
                    statusElement.className = 'status error';
                }
            });
        }

        // Test notifications
        function testNotifications() {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess('اختبار الإشعار الناجح!');
                notificationManager.showError('اختبار إشعار الخطأ!');
                notificationManager.showWarning('اختبار إشعار التحذير!');
                notificationManager.showInfo('اختبار الإشعار المعلوماتي!');
            } else {
                alert('مدير الإشعارات غير متاح');
            }
        }

        // Test user loading
        async function testUserLoading() {
            try {
                const response = await fetch('api/users.php');
                const data = await response.json();
                
                if (data.success) {
                    alert(`تم تحميل ${data.users?.length || 0} مستخدم بنجاح`);
                } else {
                    alert('فشل في تحميل المستخدمين: ' + data.message);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // Test settings save
        async function testSettingsSave() {
            try {
                const testSettings = {
                    store: {
                        name: 'متجر اختبار',
                        email: '<EMAIL>'
                    }
                };

                const response = await fetch('api/general-settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testSettings)
                });

                const data = await response.json();
                
                if (data.success) {
                    alert('تم حفظ الإعدادات بنجاح!');
                } else {
                    alert('فشل في حفظ الإعدادات: ' + data.message);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // Run tests on page load
        document.addEventListener('DOMContentLoaded', async () => {
            await testAPIEndpoints();
            testJavaScriptFiles();
            
            // Update results summary
            const successCount = document.querySelectorAll('.status.success').length;
            const errorCount = document.querySelectorAll('.status.error').length;
            const totalTests = successCount + errorCount;
            
            document.getElementById('results-summary').innerHTML = `
                <strong>إجمالي الاختبارات:</strong> ${totalTests} | 
                <strong style="color: #28a745;">نجح:</strong> ${successCount} | 
                <strong style="color: #dc3545;">فشل:</strong> ${errorCount}
            `;
        });
    </script>
</body>
</html>
