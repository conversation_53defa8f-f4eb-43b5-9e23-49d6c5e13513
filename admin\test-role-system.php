<?php
/**
 * Role Management System Test
 * اختبار نظام إدارة الأدوار
 * 
 * This script tests the complete role management system
 */

session_start();
require_once '../config/config.php';

// Set execution time limit
set_time_limit(300);

// HTML header
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; border-radius: 4px; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; font-weight: bold; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .summary-item { flex: 1; padding: 15px; border-radius: 8px; text-align: center; }
        .summary-success { background: #d4edda; color: #155724; }
        .summary-error { background: #f8d7da; color: #721c24; }
        .summary-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام إدارة الأدوار</h1>
            <p>فحص شامل لنظام إدارة الأدوار والصلاحيات</p>
        </div>

<?php

class RoleSystemTester {
    private $pdo;
    private $results = [];
    private $passedTests = 0;
    private $failedTests = 0;
    private $warningTests = 0;
    
    public function __construct() {
        try {
            $dbConfig = Config::getDbConfig();
            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
            $this->log('success', 'تم الاتصال بقاعدة البيانات بنجاح');
        } catch (Exception $e) {
            $this->log('error', 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
            throw $e;
        }
    }
    
    private function log($type, $message, $details = null) {
        $this->results[] = [
            'type' => $type,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Count test results
        switch ($type) {
            case 'success':
                $this->passedTests++;
                break;
            case 'error':
                $this->failedTests++;
                break;
            case 'warning':
                $this->warningTests++;
                break;
        }
        
        // Output immediately for real-time feedback
        $class = $type;
        echo "<div class='test-item $class'>";
        echo "<strong>" . date('H:i:s') . "</strong> - $message";
        if ($details) {
            echo "<div class='code'>$details</div>";
        }
        echo "</div>";
        flush();
    }
    
    public function testDatabaseTables() {
        $this->log('info', '🗄️ اختبار جداول قاعدة البيانات...');
        
        $requiredTables = [
            'permissions' => 'جدول الصلاحيات',
            'user_roles' => 'جدول الأدوار',
            'role_permissions' => 'جدول ربط الأدوار بالصلاحيات',
            'user_role_assignments' => 'جدول تعيين الأدوار للمستخدمين'
        ];
        
        foreach ($requiredTables as $table => $description) {
            try {
                $stmt = $this->pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $this->log('success', "✅ $description موجود");
                    
                    // Check table structure
                    $stmt = $this->pdo->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll();
                    $this->log('info', "عدد الأعمدة في $table: " . count($columns));
                } else {
                    $this->log('error', "❌ $description غير موجود");
                }
            } catch (Exception $e) {
                $this->log('error', "❌ خطأ في فحص $description: " . $e->getMessage());
            }
        }
        
        // Check users table for role_id column
        try {
            $stmt = $this->pdo->query("SHOW COLUMNS FROM users LIKE 'role_id'");
            if ($stmt->rowCount() > 0) {
                $this->log('success', '✅ عمود role_id موجود في جدول users');
            } else {
                $this->log('error', '❌ عمود role_id غير موجود في جدول users');
            }
        } catch (Exception $e) {
            $this->log('error', 'خطأ في فحص جدول users: ' . $e->getMessage());
        }
    }
    
    public function testDefaultData() {
        $this->log('info', '📋 اختبار البيانات الافتراضية...');
        
        try {
            // Test permissions
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM permissions");
            $permCount = $stmt->fetch()['count'];
            
            if ($permCount > 0) {
                $this->log('success', "✅ يوجد $permCount صلاحية في النظام");
                
                // Show permissions by category
                $stmt = $this->pdo->query("SELECT category, COUNT(*) as count FROM permissions GROUP BY category");
                $categories = $stmt->fetchAll();
                
                echo "<table>";
                echo "<tr><th>الفئة</th><th>عدد الصلاحيات</th></tr>";
                foreach ($categories as $cat) {
                    echo "<tr><td>{$cat['category']}</td><td>{$cat['count']}</td></tr>";
                }
                echo "</table>";
            } else {
                $this->log('error', '❌ لا توجد صلاحيات في النظام');
            }
            
            // Test roles
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM user_roles");
            $roleCount = $stmt->fetch()['count'];
            
            if ($roleCount > 0) {
                $this->log('success', "✅ يوجد $roleCount دور في النظام");
                
                // Show roles
                $stmt = $this->pdo->query("SELECT name, display_name_ar, level FROM user_roles ORDER BY level");
                $roles = $stmt->fetchAll();
                
                echo "<table>";
                echo "<tr><th>الدور</th><th>الاسم</th><th>المستوى</th></tr>";
                foreach ($roles as $role) {
                    echo "<tr><td>{$role['name']}</td><td>{$role['display_name_ar']}</td><td>{$role['level']}</td></tr>";
                }
                echo "</table>";
            } else {
                $this->log('error', '❌ لا توجد أدوار في النظام');
            }
            
            // Test role-permission assignments
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM role_permissions");
            $assignmentCount = $stmt->fetch()['count'];
            
            if ($assignmentCount > 0) {
                $this->log('success', "✅ يوجد $assignmentCount ربط بين الأدوار والصلاحيات");
            } else {
                $this->log('warning', '⚠️ لا توجد روابط بين الأدوار والصلاحيات');
            }
            
        } catch (Exception $e) {
            $this->log('error', 'خطأ في اختبار البيانات الافتراضية: ' . $e->getMessage());
        }
    }
    
    public function testAPIEndpoints() {
        $this->log('info', '🌐 اختبار نقاط النهاية للـ API...');
        
        $endpoints = [
            'php/role-management-api.php?action=roles' => 'Roles API',
            'php/role-management-api.php?action=permissions' => 'Permissions API',
            'php/users_management.php?action=get_roles' => 'Users Management Roles API',
            'php/users_management.php?action=get_all' => 'Users Management API'
        ];
        
        foreach ($endpoints as $endpoint => $name) {
            try {
                $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $endpoint;
                
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 10,
                        'ignore_errors' => true
                    ]
                ]);
                
                $response = @file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        if (isset($data['success']) && $data['success']) {
                            $this->log('success', "✅ $name يعمل بشكل صحيح");
                        } else {
                            $this->log('warning', "⚠️ $name يعيد خطأ: " . ($data['message'] ?? 'غير محدد'));
                        }
                    } else {
                        $this->log('warning', "⚠️ $name يعيد استجابة غير صالحة");
                    }
                } else {
                    $this->log('error', "❌ $name غير متاح");
                }
                
            } catch (Exception $e) {
                $this->log('error', "❌ خطأ في اختبار $name: " . $e->getMessage());
            }
        }
    }
    
    public function testUserRoleAssignments() {
        $this->log('info', '👤 اختبار تعيين الأدوار للمستخدمين...');
        
        try {
            // Check if users have roles assigned
            $stmt = $this->pdo->query("
                SELECT 
                    COUNT(DISTINCT u.id) as users_with_roles,
                    COUNT(DISTINCT u2.id) as total_users
                FROM users u
                LEFT JOIN users u2 ON 1=1
                WHERE u.role_id IS NOT NULL
            ");
            $result = $stmt->fetch();
            
            $usersWithRoles = $result['users_with_roles'];
            $totalUsers = $result['total_users'];
            
            if ($usersWithRoles > 0) {
                $this->log('success', "✅ $usersWithRoles من أصل $totalUsers مستخدم لديهم أدوار مُعيَّنة");
            } else {
                $this->log('warning', '⚠️ لا يوجد مستخدمين لديهم أدوار مُعيَّنة');
            }
            
            // Check role assignments table
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM user_role_assignments");
            $assignmentCount = $stmt->fetch()['count'];
            
            if ($assignmentCount > 0) {
                $this->log('success', "✅ يوجد $assignmentCount تعيين دور في جدول user_role_assignments");
            } else {
                $this->log('warning', '⚠️ لا توجد تعيينات أدوار في جدول user_role_assignments');
            }
            
        } catch (Exception $e) {
            $this->log('error', 'خطأ في اختبار تعيين الأدوار: ' . $e->getMessage());
        }
    }
    
    public function runAllTests() {
        $this->log('info', '🚀 بدء الاختبار الشامل لنظام إدارة الأدوار...');
        
        $this->testDatabaseTables();
        $this->testDefaultData();
        $this->testAPIEndpoints();
        $this->testUserRoleAssignments();
        
        return [
            'passed' => $this->passedTests,
            'failed' => $this->failedTests,
            'warnings' => $this->warningTests,
            'total' => $this->passedTests + $this->failedTests + $this->warningTests
        ];
    }
}

// Run the comprehensive test
try {
    $tester = new RoleSystemTester();
    $summary = $tester->runAllTests();
    
    echo "<div class='summary'>";
    echo "<div class='summary-item summary-success'>";
    echo "<h3>✅ نجح</h3>";
    echo "<h2>{$summary['passed']}</h2>";
    echo "</div>";
    
    echo "<div class='summary-item summary-warning'>";
    echo "<h3>⚠️ تحذيرات</h3>";
    echo "<h2>{$summary['warnings']}</h2>";
    echo "</div>";
    
    echo "<div class='summary-item summary-error'>";
    echo "<h3>❌ فشل</h3>";
    echo "<h2>{$summary['failed']}</h2>";
    echo "</div>";
    echo "</div>";
    
    if ($summary['failed'] == 0) {
        echo "<div class='test-item success'>";
        echo "<h3>🎉 تم اجتياز جميع الاختبارات الأساسية!</h3>";
        echo "<p>نظام إدارة الأدوار جاهز للاستخدام.</p>";
        echo "</div>";
    } else {
        echo "<div class='test-item error'>";
        echo "<h3>⚠️ يوجد مشاكل تحتاج إلى إصلاح</h3>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>";
    echo "<h3>❌ فشل في تشغيل الاختبارات</h3>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

        <div style="text-align: center; margin: 30px 0;">
            <a href="create-role-management-schema.php" class="btn btn-primary">
                🛡️ إنشاء مخطط الأدوار
            </a>
            <a href="populate-default-roles.php" class="btn btn-success">
                📋 إضافة البيانات الافتراضية
            </a>
            <a href="role-management-interface.html" class="btn btn-primary">
                🎭 واجهة إدارة الأدوار
            </a>
            <a href="test-users-complete.html" class="btn btn-success">
                👥 اختبار إدارة المستخدمين
            </a>
        </div>
    </div>
</body>
</html>
