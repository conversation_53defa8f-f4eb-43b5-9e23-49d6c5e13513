<?php

/**
 * Database Connection Test
 * اختبار الاتصال بقاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');

// Database configuration (from .env)
$host = 'localhost';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';
$port = 3307;

$results = [];
$results['timestamp'] = date('Y-m-d H:i:s');

// Test 1: Check if MySQL extension is loaded
$results['mysql_extension'] = extension_loaded('pdo_mysql');

// Test 2: Try to connect to MySQL server (without database)
try {
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $results['mysql_connection'] = true;
    $results['mysql_version'] = $pdo->getAttribute(PDO::ATTR_SERVER_VERSION);
} catch (PDOException $e) {
    $results['mysql_connection'] = false;
    $results['mysql_error'] = $e->getMessage();
}

// Test 3: Try to create database
if ($results['mysql_connection']) {
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $results['database_creation'] = true;
    } catch (PDOException $e) {
        $results['database_creation'] = false;
        $results['database_creation_error'] = $e->getMessage();
    }
}

// Test 4: Try to connect to specific database
if ($results['mysql_connection']) {
    try {
        $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $results['database_connection'] = true;
    } catch (PDOException $e) {
        $results['database_connection'] = false;
        $results['database_connection_error'] = $e->getMessage();
    }
}

// Test 5: Check existing tables
if (isset($pdo) && $results['database_connection']) {
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $results['existing_tables'] = $tables;
        $results['tables_count'] = count($tables);
    } catch (PDOException $e) {
        $results['tables_check_error'] = $e->getMessage();
    }
}

// Test 6: Check if general_settings table exists
if (isset($pdo) && $results['database_connection']) {
    try {
        $stmt = $pdo->query("DESCRIBE general_settings");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['general_settings_table'] = true;
        $results['general_settings_columns'] = array_column($columns, 'Field');
    } catch (PDOException $e) {
        $results['general_settings_table'] = false;
        $results['general_settings_error'] = $e->getMessage();
    }
}

// Test 7: Check if categories table exists
if (isset($pdo) && $results['database_connection']) {
    try {
        $stmt = $pdo->query("DESCRIBE categories");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['categories_table'] = true;
        $results['categories_columns'] = array_column($columns, 'Field');
    } catch (PDOException $e) {
        $results['categories_table'] = false;
        $results['categories_error'] = $e->getMessage();
    }
}

// Test 8: Check if payment_methods table exists
if (isset($pdo) && $results['database_connection']) {
    try {
        $stmt = $pdo->query("DESCRIBE payment_methods");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['payment_methods_table'] = true;
        $results['payment_methods_columns'] = array_column($columns, 'Field');
    } catch (PDOException $e) {
        $results['payment_methods_table'] = false;
        $results['payment_methods_error'] = $e->getMessage();
    }
}

// Overall status
$results['overall_status'] = $results['mysql_connection'] && $results['database_connection'];

// Recommendations
$results['recommendations'] = [];

if (!$results['mysql_extension']) {
    $results['recommendations'][] = 'تأكد من تثبيت PHP MySQL extension';
}

if (!$results['mysql_connection']) {
    $results['recommendations'][] = 'تأكد من تشغيل خادم MySQL';
    $results['recommendations'][] = 'تحقق من إعدادات الاتصال (host, port, username, password)';
}

if (!isset($results['general_settings_table']) || !$results['general_settings_table']) {
    $results['recommendations'][] = 'قم بتشغيل سكريبت إعداد قاعدة البيانات';
}

// Configuration info
$results['configuration'] = [
    'host' => $host,
    'port' => $port,
    'database' => $dbname,
    'username' => $username,
    'password_set' => !empty($password)
];

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
