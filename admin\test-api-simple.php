<?php
/**
 * Simple API Test
 * اختبار API مبسط
 */

echo "<h2>اختبار API الفئات</h2>\n";

// Test different actions
$tests = [
    'get_all' => 'جلب جميع الفئات',
    'get_by_id&id=1' => 'جلب فئة واحدة',
    'search&q=كتب' => 'البحث في الفئات'
];

foreach ($tests as $action => $description) {
    echo "<h3>$description</h3>\n";
    echo "<p><strong>الطلب:</strong> php/categories.php?action=$action</p>\n";
    
    try {
        $url = "php/categories.php?action=$action";
        $response = file_get_contents($url);
        
        if ($response === false) {
            echo "<p style='color: red;'>❌ فشل في الطلب</p>\n";
        } else {
            $data = json_decode($response, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                if ($data['success']) {
                    echo "<p style='color: green;'>✅ نجح الطلب</p>\n";
                    echo "<details><summary>عرض النتيجة</summary><pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre></details>\n";
                } else {
                    echo "<p style='color: red;'>❌ فشل: " . $data['message'] . "</p>\n";
                }
            } else {
                echo "<p style='color: red;'>❌ استجابة غير صحيحة: $response</p>\n";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<hr>\n";
}

// Test POST request
echo "<h3>اختبار إنشاء فئة (POST)</h3>\n";

$testData = [
    'action' => 'create',
    'name_ar' => 'فئة اختبار API',
    'name_en' => 'API Test Category',
    'slug' => 'api-test-' . time(),
    'description_ar' => 'فئة للاختبار',
    'icon' => 'fas fa-test',
    'color' => '#ff6b6b',
    'is_active' => 1,
    'is_featured' => 0
];

$postData = http_build_query($testData);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/x-www-form-urlencoded',
        'content' => $postData
    ]
]);

try {
    $response = file_get_contents('php/categories.php', false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>❌ فشل في طلب POST</p>\n";
    } else {
        $data = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            if ($data['success']) {
                echo "<p style='color: green;'>✅ نجح إنشاء الفئة</p>\n";
                echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
            } else {
                echo "<p style='color: red;'>❌ فشل إنشاء الفئة: " . $data['message'] . "</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ استجابة غير صحيحة: $response</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في POST: " . $e->getMessage() . "</p>\n";
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        p {
            margin: 8px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9em;
        }
        details {
            margin: 10px 0;
        }
        summary {
            cursor: pointer;
            font-weight: bold;
            color: #667eea;
        }
        hr {
            margin: 20px 0;
            border: none;
            border-top: 1px solid #e0e0e0;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
        <a href="php/categories.php?action=get_all" class="back-link" style="background: #28a745;" target="_blank">📊 API مباشر</a>
    </div>
</body>
</html>
