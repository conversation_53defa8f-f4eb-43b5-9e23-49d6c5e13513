<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قسم الإعدادات العامة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/general-settings.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .test-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        
        .test-btn.info {
            background: #17a2b8;
            color: white;
        }
        
        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-results {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #667eea;
        }
        
        .result-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .result-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .settings-preview {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        #generalSettingsContent {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cog"></i> اختبار قسم الإعدادات العامة</h1>
            <p>اختبار شامل لجميع وظائف الإعدادات العامة</p>
        </div>
        
        <div class="test-content">
            <!-- Test Database Connection -->
            <div class="test-section">
                <h3><i class="fas fa-database"></i> اختبار الاتصال بقاعدة البيانات</h3>
                <p>تحقق من الاتصال بقاعدة البيانات ووجود الجداول المطلوبة</p>
                <div class="test-buttons">
                    <a href="setup/test_database_connection.php" class="test-btn info" target="_blank">
                        <i class="fas fa-database"></i> اختبار الاتصال
                    </a>
                    <a href="setup/create_tables_simple.php" class="test-btn success" target="_blank">
                        <i class="fas fa-plus"></i> إنشاء الجداول
                    </a>
                </div>
            </div>
            
            <!-- Test API Endpoints -->
            <div class="test-section">
                <h3><i class="fas fa-code"></i> اختبار واجهة البرمجة (API)</h3>
                <p>اختبار جميع نقاط النهاية لواجهة البرمجة</p>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="testGetAllSettings()">
                        <i class="fas fa-download"></i> جلب جميع الإعدادات
                    </button>
                    <button class="test-btn warning" onclick="testGetSingleSetting()">
                        <i class="fas fa-search"></i> جلب إعداد واحد
                    </button>
                    <a href="php/general_settings.php?action=get_all" class="test-btn info" target="_blank">
                        <i class="fas fa-external-link-alt"></i> عرض JSON مباشر
                    </a>
                </div>
                <div id="apiResults" class="test-results" style="display: none;">
                    <h4>نتائج اختبار API:</h4>
                    <div id="apiResultsContent"></div>
                </div>
            </div>
            
            <!-- Test User Interface -->
            <div class="test-section">
                <h3><i class="fas fa-desktop"></i> اختبار واجهة المستخدم</h3>
                <p>اختبار تحميل وعرض نموذج الإعدادات العامة</p>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="loadGeneralSettingsContent()">
                        <i class="fas fa-play"></i> تحميل واجهة الإعدادات
                    </button>
                    <button class="test-btn warning" onclick="clearSettingsContent()">
                        <i class="fas fa-trash"></i> مسح المحتوى
                    </button>
                </div>
                <div id="generalSettingsContent" class="settings-preview"></div>
            </div>
            
            <!-- Test Results Summary -->
            <div class="test-section">
                <h3><i class="fas fa-chart-line"></i> ملخص نتائج الاختبار</h3>
                <div id="testSummary" class="test-results">
                    <div class="result-info">
                        <i class="fas fa-info-circle"></i> ابدأ الاختبارات لرؤية النتائج هنا
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/general-settings-new.js"></script>
    <script>
        let testResults = {
            database: false,
            api: false,
            ui: false
        };
        
        function updateTestSummary() {
            const summary = document.getElementById('testSummary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r).length;
            
            let html = `
                <div class="result-info">
                    <strong>إجمالي الاختبارات:</strong> ${total} | 
                    <strong>نجح:</strong> ${passed} | 
                    <strong>فشل:</strong> ${total - passed}
                </div>
            `;
            
            for (const [test, result] of Object.entries(testResults)) {
                const status = result ? 'نجح' : 'لم يتم الاختبار';
                const className = result ? 'result-success' : 'result-info';
                const icon = result ? 'fa-check-circle' : 'fa-clock';
                
                html += `
                    <div class="${className}">
                        <i class="fas ${icon}"></i> ${test}: ${status}
                    </div>
                `;
            }
            
            summary.innerHTML = html;
        }
        
        async function testGetAllSettings() {
            const resultsDiv = document.getElementById('apiResults');
            const contentDiv = document.getElementById('apiResultsContent');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '<div class="result-info"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</div>';
            
            try {
                const response = await fetch('php/general_settings.php?action=get_all');
                const data = await response.json();
                
                if (data.success) {
                    testResults.api = true;
                    contentDiv.innerHTML = `
                        <div class="result-success">
                            <i class="fas fa-check-circle"></i> نجح جلب الإعدادات
                        </div>
                        <div class="result-info">
                            عدد مجموعات الإعدادات: ${Object.keys(data.data).length}
                        </div>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(data.message || 'فشل في جلب الإعدادات');
                }
            } catch (error) {
                testResults.api = false;
                contentDiv.innerHTML = `
                    <div class="result-error">
                        <i class="fas fa-exclamation-triangle"></i> فشل الاختبار: ${error.message}
                    </div>
                `;
            }
            
            updateTestSummary();
        }
        
        async function testGetSingleSetting() {
            const resultsDiv = document.getElementById('apiResults');
            const contentDiv = document.getElementById('apiResultsContent');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '<div class="result-info"><i class="fas fa-spinner fa-spin"></i> جاري اختبار جلب إعداد واحد...</div>';
            
            try {
                const response = await fetch('php/general_settings.php?action=get_setting&key=site_name');
                const data = await response.json();
                
                if (data.success) {
                    contentDiv.innerHTML += `
                        <div class="result-success">
                            <i class="fas fa-check-circle"></i> نجح جلب إعداد site_name: "${data.value}"
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'فشل في جلب الإعداد');
                }
            } catch (error) {
                contentDiv.innerHTML += `
                    <div class="result-error">
                        <i class="fas fa-exclamation-triangle"></i> فشل اختبار الإعداد الواحد: ${error.message}
                    </div>
                `;
            }
        }
        
        function clearSettingsContent() {
            document.getElementById('generalSettingsContent').innerHTML = '';
            testResults.ui = false;
            updateTestSummary();
        }
        
        // Override the original function to track UI test results
        const originalLoadFunction = window.loadGeneralSettingsContent;
        window.loadGeneralSettingsContent = function() {
            originalLoadFunction();
            
            // Check if content loaded successfully after a delay
            setTimeout(() => {
                const content = document.getElementById('generalSettingsContent');
                if (content && content.innerHTML.trim() !== '') {
                    testResults.ui = true;
                    updateTestSummary();
                }
            }, 2000);
        };
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTestSummary();
        });
    </script>
</body>
</html>
