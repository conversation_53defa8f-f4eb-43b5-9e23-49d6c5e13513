<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات الحرجة</title>
    <link rel="stylesheet" href="css/multi-user-admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            text-align: center;
        }
        .test-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-pass {
            background: #28a745;
        }
        .status-fail {
            background: #dc3545;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            direction: ltr;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-tools"></i> اختبار الإصلاحات الحرجة</h1>
        <p>هذه الصفحة تختبر جميع الإصلاحات الحرجة المطبقة على النظام.</p>

        <!-- Product Management Functions Test -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار وظائف إدارة المنتجات</h3>
            <p>فحص ما إذا كانت وظائف إدارة المنتجات تعمل بشكل صحيح</p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-edit"></i> تعديل المنتج</h4>
                    <button class="test-button" onclick="testEditProduct()">اختبار editProduct</button>
                    <div id="editProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-eye"></i> عرض المنتج</h4>
                    <button class="test-button" onclick="testViewProduct()">اختبار viewProduct</button>
                    <div id="viewProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-trash"></i> حذف المنتج</h4>
                    <button class="test-button" onclick="testDeleteProduct()">اختبار deleteProduct</button>
                    <div id="deleteProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-rocket"></i> صفحة الهبوط</h4>
                    <button class="test-button" onclick="testViewLandingPage()">اختبار viewLandingPage</button>
                    <div id="viewLandingPageResult"></div>
                </div>
            </div>
        </div>

        <!-- Pagination Functions Test -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> اختبار وظائف التصفح</h3>
            <p>فحص وظائف التصفح والانتقال بين الصفحات</p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-arrow-right"></i> الانتقال للصفحة</h4>
                    <button class="test-button" onclick="testGoToProductsPage()">اختبار goToProductsPage</button>
                    <div id="goToProductsPageResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-arrow-left"></i> الصفحة السابقة</h4>
                    <button class="test-button" onclick="testPreviousProductsPage()">اختبار previousProductsPage</button>
                    <div id="previousProductsPageResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-arrow-right"></i> الصفحة التالية</h4>
                    <button class="test-button" onclick="testNextProductsPage()">اختبار nextProductsPage</button>
                    <div id="nextProductsPageResult"></div>
                </div>
            </div>
        </div>

        <!-- Settings Sections Test -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> اختبار أقسام الإعدادات</h3>
            <p>فحص جميع أقسام الإعدادات</p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4>الإعدادات العامة</h4>
                    <button class="test-button" onclick="testSettingsSection('generalSettings')">اختبار</button>
                    <div id="generalSettingsResult"></div>
                </div>
                
                <div class="test-card">
                    <h4>إعدادات المتجر</h4>
                    <button class="test-button" onclick="testSettingsSection('storeSettings')">اختبار</button>
                    <div id="storeSettingsResult"></div>
                </div>
                
                <div class="test-card">
                    <h4>إدارة المستخدمين</h4>
                    <button class="test-button" onclick="testSettingsSection('userManagement')">اختبار</button>
                    <div id="userManagementResult"></div>
                </div>
                
                <div class="test-card">
                    <h4>إدارة الفئات</h4>
                    <button class="test-button" onclick="testSettingsSection('categoriesManagement')">اختبار</button>
                    <div id="categoriesManagementResult"></div>
                </div>
            </div>
        </div>

        <!-- Reports Section Test -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> اختبار قسم التقارير</h3>
            <p>فحص قسم التقارير والإحصائيات</p>
            <button class="test-button" onclick="testReportsSection()">اختبار التقارير</button>
            <div id="reportsResult"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> مخرجات وحدة التحكم</h3>
            <div id="consoleOutput" class="console-output">جاهز لعرض مخرجات الاختبارات...</div>
        </div>

        <!-- Run All Tests -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" style="background: #28a745; font-size: 16px; padding: 15px 30px;" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
            <button class="test-button" style="background: #6c757d; font-size: 16px; padding: 15px 30px;" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
        </div>
    </div>

    <!-- Load the JavaScript files in the same order as the main page -->
    <script src="js/product-management-functions.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/products-pagination.js"></script>
    <script src="js/critical-fixes.js"></script>

    <script>
        // Console capture for testing
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        let consoleOutput = '';

        function captureConsole() {
            console.log = function(...args) {
                consoleOutput += '[LOG] ' + args.join(' ') + '\n';
                originalConsole.log.apply(console, args);
                updateConsoleDisplay();
            };

            console.error = function(...args) {
                consoleOutput += '[ERROR] ' + args.join(' ') + '\n';
                originalConsole.error.apply(console, args);
                updateConsoleDisplay();
            };

            console.warn = function(...args) {
                consoleOutput += '[WARN] ' + args.join(' ') + '\n';
                originalConsole.warn.apply(console, args);
                updateConsoleDisplay();
            };
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('consoleOutput');
            if (consoleDiv) {
                consoleDiv.textContent = consoleOutput || 'لا توجد مخرجات...';
                consoleDiv.scrollTop = consoleDiv.scrollHeight;
            }
        }

        function clearConsole() {
            consoleOutput = '';
            updateConsoleDisplay();
        }

        // Test functions
        function testEditProduct() {
            const resultDiv = document.getElementById('editProductResult');
            try {
                if (typeof window.editProduct === 'function') {
                    console.log('Testing editProduct function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testViewProduct() {
            const resultDiv = document.getElementById('viewProductResult');
            try {
                if (typeof window.viewProduct === 'function') {
                    console.log('Testing viewProduct function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testDeleteProduct() {
            const resultDiv = document.getElementById('deleteProductResult');
            try {
                if (typeof window.deleteProduct === 'function') {
                    console.log('Testing deleteProduct function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testViewLandingPage() {
            const resultDiv = document.getElementById('viewLandingPageResult');
            try {
                if (typeof window.viewLandingPage === 'function') {
                    console.log('Testing viewLandingPage function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testGoToProductsPage() {
            const resultDiv = document.getElementById('goToProductsPageResult');
            try {
                if (typeof window.goToProductsPage === 'function') {
                    console.log('Testing goToProductsPage function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testPreviousProductsPage() {
            const resultDiv = document.getElementById('previousProductsPageResult');
            try {
                if (typeof window.previousProductsPage === 'function') {
                    console.log('Testing previousProductsPage function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testNextProductsPage() {
            const resultDiv = document.getElementById('nextProductsPageResult');
            try {
                if (typeof window.nextProductsPage === 'function') {
                    console.log('Testing nextProductsPage function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testSettingsSection(section) {
            const resultDiv = document.getElementById(`${section}Result`);
            const functionName = `load${section.charAt(0).toUpperCase() + section.slice(1)}Content`;
            
            try {
                if (typeof window[functionName] === 'function') {
                    console.log(`Testing ${functionName} function...`);
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function testReportsSection() {
            const resultDiv = document.getElementById('reportsResult');
            try {
                if (typeof window.loadReportsContent === 'function') {
                    console.log('Testing loadReportsContent function...');
                    resultDiv.innerHTML = '<span class="status-indicator status-pass"></span>متاحة';
                } else {
                    resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>غير متاحة';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="status-indicator status-fail"></span>خطأ: ' + error.message;
            }
        }

        function runAllTests() {
            clearConsole();
            console.log('🚀 بدء تشغيل جميع اختبارات الإصلاحات الحرجة...');
            
            testEditProduct();
            testViewProduct();
            testDeleteProduct();
            testViewLandingPage();
            testGoToProductsPage();
            testPreviousProductsPage();
            testNextProductsPage();
            testSettingsSection('generalSettings');
            testSettingsSection('storeSettings');
            testSettingsSection('userManagement');
            testSettingsSection('categoriesManagement');
            testReportsSection();
            
            console.log('✅ انتهاء جميع الاختبارات.');
        }

        function clearResults() {
            const resultDivs = [
                'editProductResult', 'viewProductResult', 'deleteProductResult', 
                'viewLandingPageResult', 'goToProductsPageResult', 'previousProductsPageResult',
                'nextProductsPageResult', 'generalSettingsResult', 'storeSettingsResult',
                'userManagementResult', 'categoriesManagementResult', 'reportsResult'
            ];
            
            resultDivs.forEach(id => {
                const div = document.getElementById(id);
                if (div) div.innerHTML = '';
            });
            
            clearConsole();
        }

        // Initialize console capture
        captureConsole();
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
