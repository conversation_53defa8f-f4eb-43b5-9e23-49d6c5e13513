<?php
/**
 * Fix Database Issues
 * إصلاح مشاكل قاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');

require_once 'config/db_env.php';

$results = [];
$results['timestamp'] = date('Y-m-d H:i:s');

try {
    // Check if general_settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
    $tableExists = $stmt->rowCount() > 0;
    
    $results['general_settings_exists'] = $tableExists;
    
    if ($tableExists) {
        // Check table structure
        $stmt = $pdo->query("DESCRIBE general_settings");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['current_columns'] = array_column($columns, 'Field');
        
        // Check if required columns exist
        $requiredColumns = ['setting_key', 'setting_value', 'data_type', 'category', 'description_ar'];
        $missingColumns = array_diff($requiredColumns, $results['current_columns']);
        $results['missing_columns'] = $missingColumns;
        
        if (!empty($missingColumns)) {
            $results['action'] = 'recreate_table';
            
            // Drop and recreate table
            $pdo->exec("DROP TABLE IF EXISTS general_settings");
            
            // Create table with correct structure
            $createTableSQL = "
                CREATE TABLE general_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(255) NOT NULL UNIQUE,
                    setting_value TEXT,
                    data_type ENUM('string', 'number', 'boolean', 'json', 'email', 'url', 'color', 'file') DEFAULT 'string',
                    category VARCHAR(100) NOT NULL,
                    description_ar TEXT,
                    description_en TEXT,
                    is_public BOOLEAN DEFAULT FALSE,
                    validation_rules JSON,
                    default_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_category (category),
                    INDEX idx_public (is_public)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $pdo->exec($createTableSQL);
            $results['table_recreated'] = true;
            
            // Insert default settings
            $defaultSettings = [
                ['site_name', 'موقع تجريبي', 'string', 'site', 'اسم الموقع', 'Site Name'],
                ['site_description', 'وصف الموقع التجريبي', 'string', 'site', 'وصف الموقع', 'Site Description'],
                ['site_logo', '', 'url', 'site', 'شعار الموقع', 'Site Logo'],
                ['site_favicon', '', 'url', 'site', 'أيقونة الموقع', 'Site Favicon'],
                ['site_language', 'ar', 'string', 'localization', 'لغة الموقع', 'Site Language'],
                ['site_timezone', 'Africa/Algiers', 'string', 'localization', 'المنطقة الزمنية', 'Timezone'],
                ['maintenance_mode', 'false', 'boolean', 'maintenance', 'وضع الصيانة', 'Maintenance Mode'],
                ['maintenance_message', 'الموقع تحت الصيانة', 'string', 'maintenance', 'رسالة الصيانة', 'Maintenance Message'],
                ['email_from_name', 'موقع تجريبي', 'string', 'email', 'اسم المرسل', 'From Name'],
                ['email_from_address', '<EMAIL>', 'email', 'email', 'بريد المرسل', 'From Email'],
                ['smtp_host', '', 'string', 'email', 'خادم SMTP', 'SMTP Host'],
                ['smtp_port', '587', 'number', 'email', 'منفذ SMTP', 'SMTP Port'],
                ['smtp_username', '', 'string', 'email', 'اسم مستخدم SMTP', 'SMTP Username'],
                ['smtp_password', '', 'string', 'email', 'كلمة مرور SMTP', 'SMTP Password'],
                ['smtp_encryption', 'tls', 'string', 'email', 'تشفير SMTP', 'SMTP Encryption'],
                ['seo_title', 'موقع تجريبي', 'string', 'seo', 'عنوان SEO', 'SEO Title'],
                ['seo_description', 'وصف الموقع لمحركات البحث', 'string', 'seo', 'وصف SEO', 'SEO Description'],
                ['seo_keywords', 'موقع, تجريبي, اختبار', 'string', 'seo', 'كلمات مفتاحية', 'SEO Keywords'],
                ['google_analytics_id', '', 'string', 'analytics', 'معرف Google Analytics', 'Google Analytics ID'],
                ['facebook_pixel_id', '', 'string', 'analytics', 'معرف Facebook Pixel', 'Facebook Pixel ID'],
                ['social_facebook', '', 'url', 'social', 'رابط فيسبوك', 'Facebook URL'],
                ['social_twitter', '', 'url', 'social', 'رابط تويتر', 'Twitter URL'],
                ['social_instagram', '', 'url', 'social', 'رابط إنستغرام', 'Instagram URL'],
                ['social_linkedin', '', 'url', 'social', 'رابط لينكد إن', 'LinkedIn URL'],
                ['api_rate_limit', '1000', 'number', 'api', 'حد معدل API', 'API Rate Limit'],
                ['api_timeout', '30', 'number', 'api', 'مهلة API بالثواني', 'API Timeout'],
                ['backup_frequency', 'daily', 'string', 'maintenance', 'تكرار النسخ الاحتياطي', 'Backup Frequency'],
                ['backup_retention', '30', 'number', 'maintenance', 'مدة الاحتفاظ بالنسخ (أيام)', 'Backup Retention Days']
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO general_settings 
                (setting_key, setting_value, data_type, category, description_ar, description_en, default_value) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $insertedCount = 0;
            foreach ($defaultSettings as $setting) {
                try {
                    $insertStmt->execute([
                        $setting[0], // setting_key
                        $setting[1], // setting_value
                        $setting[2], // data_type
                        $setting[3], // category
                        $setting[4], // description_ar
                        $setting[5], // description_en
                        $setting[1]  // default_value (same as setting_value)
                    ]);
                    $insertedCount++;
                } catch (PDOException $e) {
                    $results['insert_errors'][] = "Error inserting {$setting[0]}: " . $e->getMessage();
                }
            }
            
            $results['default_settings_inserted'] = $insertedCount;
        }
    } else {
        $results['action'] = 'create_table';
        
        // Create table
        $createTableSQL = "
            CREATE TABLE general_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) NOT NULL UNIQUE,
                setting_value TEXT,
                data_type ENUM('string', 'number', 'boolean', 'json', 'email', 'url', 'color', 'file') DEFAULT 'string',
                category VARCHAR(100) NOT NULL,
                description_ar TEXT,
                description_en TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                validation_rules JSON,
                default_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_public (is_public)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        $results['table_created'] = true;
        
        // Insert default settings (same as above)
        $defaultSettings = [
            ['site_name', 'موقع تجريبي', 'string', 'site', 'اسم الموقع', 'Site Name'],
            ['site_description', 'وصف الموقع التجريبي', 'string', 'site', 'وصف الموقع', 'Site Description'],
            ['site_logo', '', 'url', 'site', 'شعار الموقع', 'Site Logo'],
            ['site_favicon', '', 'url', 'site', 'أيقونة الموقع', 'Site Favicon'],
            ['site_language', 'ar', 'string', 'localization', 'لغة الموقع', 'Site Language'],
            ['site_timezone', 'Africa/Algiers', 'string', 'localization', 'المنطقة الزمنية', 'Timezone'],
            ['maintenance_mode', 'false', 'boolean', 'maintenance', 'وضع الصيانة', 'Maintenance Mode'],
            ['maintenance_message', 'الموقع تحت الصيانة', 'string', 'maintenance', 'رسالة الصيانة', 'Maintenance Message'],
            ['email_from_name', 'موقع تجريبي', 'string', 'email', 'اسم المرسل', 'From Name'],
            ['email_from_address', '<EMAIL>', 'email', 'email', 'بريد المرسل', 'From Email'],
            ['smtp_host', '', 'string', 'email', 'خادم SMTP', 'SMTP Host'],
            ['smtp_port', '587', 'number', 'email', 'منفذ SMTP', 'SMTP Port'],
            ['smtp_username', '', 'string', 'email', 'اسم مستخدم SMTP', 'SMTP Username'],
            ['smtp_password', '', 'string', 'email', 'كلمة مرور SMTP', 'SMTP Password'],
            ['smtp_encryption', 'tls', 'string', 'email', 'تشفير SMTP', 'SMTP Encryption'],
            ['seo_title', 'موقع تجريبي', 'string', 'seo', 'عنوان SEO', 'SEO Title'],
            ['seo_description', 'وصف الموقع لمحركات البحث', 'string', 'seo', 'وصف SEO', 'SEO Description'],
            ['seo_keywords', 'موقع, تجريبي, اختبار', 'string', 'seo', 'كلمات مفتاحية', 'SEO Keywords'],
            ['google_analytics_id', '', 'string', 'analytics', 'معرف Google Analytics', 'Google Analytics ID'],
            ['facebook_pixel_id', '', 'string', 'analytics', 'معرف Facebook Pixel', 'Facebook Pixel ID'],
            ['social_facebook', '', 'url', 'social', 'رابط فيسبوك', 'Facebook URL'],
            ['social_twitter', '', 'url', 'social', 'رابط تويتر', 'Twitter URL'],
            ['social_instagram', '', 'url', 'social', 'رابط إنستغرام', 'Instagram URL'],
            ['social_linkedin', '', 'url', 'social', 'رابط لينكد إن', 'LinkedIn URL'],
            ['api_rate_limit', '1000', 'number', 'api', 'حد معدل API', 'API Rate Limit'],
            ['api_timeout', '30', 'number', 'api', 'مهلة API بالثواني', 'API Timeout'],
            ['backup_frequency', 'daily', 'string', 'maintenance', 'تكرار النسخ الاحتياطي', 'Backup Frequency'],
            ['backup_retention', '30', 'number', 'maintenance', 'مدة الاحتفاظ بالنسخ (أيام)', 'Backup Retention Days']
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO general_settings 
            (setting_key, setting_value, data_type, category, description_ar, description_en, default_value) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $insertedCount = 0;
        foreach ($defaultSettings as $setting) {
            try {
                $insertStmt->execute([
                    $setting[0], // setting_key
                    $setting[1], // setting_value
                    $setting[2], // data_type
                    $setting[3], // category
                    $setting[4], // description_ar
                    $setting[5], // description_en
                    $setting[1]  // default_value (same as setting_value)
                ]);
                $insertedCount++;
            } catch (PDOException $e) {
                $results['insert_errors'][] = "Error inserting {$setting[0]}: " . $e->getMessage();
            }
        }
        
        $results['default_settings_inserted'] = $insertedCount;
    }
    
    // Final verification
    $stmt = $pdo->query("SELECT COUNT(*) FROM general_settings");
    $results['final_count'] = $stmt->fetchColumn();
    
    $results['success'] = true;
    $results['message'] = 'Database fixed successfully';
    
} catch (PDOException $e) {
    $results['success'] = false;
    $results['error'] = $e->getMessage();
}

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
