/**
 * Users Management JavaScript
 * إدارة المستخدمين - JavaScript
 */

console.log('👥 تحميل ملف users-management.js...');

// Global variables
let usersData = {};
let rolesData = {};
let permissionsData = {};
let editingUserId = null;
let currentPage = 1;
let currentFilters = {
    search: '',
    role: '',
    status: ''
};

/**
 * Force load roles data
 */
async function forceLoadRolesNow() {
    try {
        console.log('⚡ إجبار تحميل إدارة الأدوار التفاعلية الآن...');
        const response = await fetch('../php/api/roles-fixed.php?action=list');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.success) {
            rolesData = data.data;
            console.log('✅ تم تحميل الأدوار بنجاح:', rolesData);
            return data;
        } else {
            throw new Error(data.message || 'خطأ غير معروف');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الأدوار:', error);
        throw error;
    }
};

/**
 * Load users management content
 */
function loadUsersManagementContent() {
    console.log('👥 بدء تحميل إدارة المستخدمين...');

    const container = document.getElementById('usersManagementContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إدارة المستخدمين');
        return;
    }

    console.log('✅ تم العثور على الحاوي');

    // Show loading
    showLoadingState(container);

    // Fetch all required data
    Promise.all([
        fetchUsersData(),
        fetchRolesData(),
        fetchPermissionsData()
    ])
    .then(([usersResponse, rolesResponse, permissionsResponse]) => {
        console.log('📦 تم استلام جميع البيانات:', {
            users: usersResponse,
            roles: rolesResponse,
            permissions: permissionsResponse
        });

        if (usersResponse.success) {
            usersData = usersResponse.data;
        }
        if (rolesResponse.success) {
            rolesData = rolesResponse.data;
        }
        if (permissionsResponse.success) {
            permissionsData = permissionsResponse.data;
        }

        renderInteractiveUsersInterface(container, {
            users: usersData,
            roles: rolesData,
            permissions: permissionsData
        });
    })
    .catch(error => {
        console.error('❌ خطأ في تحميل المستخدمين:', error);
        showErrorState(container, error.message);
    });
}

/**
 * Fetch users data from server
 */
async function fetchUsersData(page = 1, limit = 20, search = '', role = '', status = '') {
    try {
        const params = new URLSearchParams({
            action: 'get_all',
            page: page,
            limit: limit,
            search: search,
            role: role,
            status: status
        });

        const url = `php/users_management.php?${params}`;
        console.log('🌐 طلب البيانات من:', url);

        const response = await fetch(url);
        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📋 البيانات المستلمة:', data);

        return data;
    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Fetch roles data from server
 */
async function fetchRolesData() {
    try {
        const url = '../php/api/roles-fixed.php?action=list';
        console.log('🌐 طلب بيانات الأدوار من:', url);

        const response = await fetch(url);
        console.log('📡 استجابة الخادم للأدوار:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📋 بيانات الأدوار المستلمة:', data);

        return data;
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات الأدوار:', error);
        return { success: false, message: error.message, data: { roles: [] } };
    }
}

/**
 * Fetch permissions data from server
 */
async function fetchPermissionsData() {
    try {
        const url = '../php/api/roles-fixed.php?action=permissions';
        console.log('🌐 طلب بيانات الصلاحيات من:', url);

        const response = await fetch(url);
        console.log('📡 استجابة الخادم للصلاحيات:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📋 بيانات الصلاحيات المستلمة:', data);

        return data;
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات الصلاحيات:', error);
        return { success: false, message: error.message, data: { permissions: [] } };
    }
}

/**
 * Show loading state
 */
function showLoadingState(container) {
    console.log('⏳ عرض حالة التحميل...');

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div>
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل إدارة المستخدمين...</p>
            <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
            <button onclick="window.location.reload()" style="margin-top: 15px; padding: 8px 16px; background-color: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">
                <i class="fas fa-sync-alt"></i> إعادة تحميل الصفحة
            </button>
        </div>
    `;
}

/**
 * Show error state
 */
function showErrorState(container, message) {
    console.log('❌ عرض حالة الخطأ:', message);

    container.innerHTML = `
        <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
            <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إدارة المستخدمين</h3>
            <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button onclick="loadUsersManagementContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <a href="php/users_management.php?action=get_all" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                    <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                </a>
            </div>
        </div>
    `;
}

/**
 * Render interactive users management interface
 */
function renderInteractiveUsersInterface(container, allData) {
    console.log('🎨 رسم واجهة إدارة المستخدمين التفاعلية...');

    const users = allData.users?.users || [];
    const roles = allData.roles?.roles || [];
    const permissions = allData.permissions?.permissions || [];
    const stats = allData.users?.stats || {};

    // Store data globally for modal use
    window.allUsersData = users;
    window.allRolesData = roles;
    window.allPermissionsData = permissions;

    const html = `
        <div style="max-width: 1400px; margin: 0 auto; padding: 20px;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                <h2 style="margin: 0;"><i class="fas fa-users"></i> إدارة المستخدمين التفاعلية</h2>
                <p style="margin: 10px 0 0 0;">إدارة حسابات المستخدمين والأدوار والصلاحيات</p>
                <div style="margin-top: 20px;">
                    <button onclick="showAddUserModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                    </button>
                    <button onclick="loadUsersManagementContent()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                    <button onclick="showRolesManagement()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-user-shield"></i> إدارة الأدوار
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-users"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.total_users || users.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">إجمالي المستخدمين</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-user-check"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.active_users || users.filter(u => u.is_active == 1).length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">المستخدمين النشطين</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-user-shield"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${roles.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">الأدوار المتاحة</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #dc3545; margin-bottom: 15px;"><i class="fas fa-user-times"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.inactive_users || users.filter(u => u.is_active == 0).length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">المستخدمين المعطلين</p>
                </div>
            </div>

            <!-- Search and Filters -->
            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 15px; align-items: end;">
                    <div>
                        <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">البحث</label>
                        <input type="text" id="userSearch" placeholder="البحث بالاسم أو البريد الإلكتروني..." style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                    </div>
                    <div>
                        <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الدور</label>
                        <select id="roleFilter" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            <option value="">جميع الأدوار</option>
                            ${roles.map(role => `<option value="${role.id}">${role.display_name_ar}</option>`).join('')}
                        </select>
                    </div>
                    <div>
                        <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الحالة</label>
                        <select id="statusFilter" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            <option value="">جميع الحالات</option>
                            <option value="1">نشط</option>
                            <option value="0">معطل</option>
                        </select>
                    </div>
                    <div>
                        <button onclick="applyUsersFilters()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;"><i class="fas fa-list"></i> قائمة المستخدمين</h3>
                    <button onclick="showAddUserModal()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-user-plus"></i> إضافة مستخدم
                    </button>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 15px; text-align: right; border-bottom: 1px solid #e0e0e0; font-weight: 600;">المستخدم</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الدور</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الحالة</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">آخر دخول</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${renderUsersTableRows(users, roles)}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة المستخدمين التفاعلية بنجاح!</strong>
                <br>يمكنك الآن إضافة وتعديل وحذف المستخدمين وإدارة الأدوار والصلاحيات.
            </div>
        </div>

        <!-- User Modal -->
        <div id="userModal" style="display: none;"></div>

        <!-- Roles Modal -->
        <div id="rolesModal" style="display: none;"></div>
    `;

    container.innerHTML = html;
}

/**
 * Render users table rows
 */
function renderUsersTableRows(users, roles) {
    if (!users || users.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 40px; text-align: center; color: #666;">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p style="margin: 0; font-size: 1.1rem;">لا يوجد مستخدمين للعرض</p>
                </td>
            </tr>
        `;
    }

    return users.map(user => {
        // Determine user type and styling
        const isAdmin = user.user_type === 'admin';
        const userTypeColor = isAdmin ? '#dc3545' : '#007bff';
        const userTypeIcon = isAdmin ? 'fas fa-user-shield' : 'fas fa-user';
        const userTypeName = isAdmin ? 'مدير' : 'مستخدم';

        // Get role information
        const roleName = user.role_name || (isAdmin ? (user.role === 'super_admin' ? 'مدير عام' : 'مدير') : 'غير محدد');
        const roleColor = isAdmin ? '#dc3545' : '#667eea';

        // Format last login
        const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : 'لم يسجل دخول';

        // Determine status - handle both is_active and status fields
        const isActive = user.status === 'active' || user.is_active == 1;
        const statusColor = isActive ? '#28a745' : '#dc3545';
        const statusText = isActive ? 'نشط' : 'معطل';

        return `
            <tr style="border-bottom: 1px solid #f0f0f0; ${isAdmin ? 'background: rgba(220, 53, 69, 0.05);' : ''}">
                <td style="padding: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: ${statusColor}; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; position: relative;">
                            ${user.first_name ? user.first_name.charAt(0).toUpperCase() : (isAdmin ? 'A' : 'U')}
                            ${isAdmin ? '<div style="position: absolute; top: -2px; right: -2px; width: 16px; height: 16px; background: #dc3545; border-radius: 50%; display: flex; align-items: center; justify-content: center;"><i class="fas fa-crown" style="font-size: 8px; color: white;"></i></div>' : ''}
                        </div>
                        <div>
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
                                <strong style="color: #333; font-size: 1.1rem;">${user.first_name || ''} ${user.last_name || ''}</strong>
                                <span style="padding: 2px 6px; background: ${userTypeColor}; color: white; border-radius: 8px; font-size: 0.7rem; font-weight: bold;">
                                    <i class="${userTypeIcon}"></i> ${userTypeName}
                                </span>
                            </div>
                            <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">${user.email}</p>
                            ${user.phone ? `<p style="margin: 2px 0 0 0; color: #999; font-size: 0.8em;"><i class="fas fa-phone"></i> ${user.phone}</p>` : ''}
                            ${user.migrated_from && user.migrated_from !== 'users' ? `<p style="margin: 2px 0 0 0; color: #17a2b8; font-size: 0.7em;"><i class="fas fa-database"></i> مرحل من: ${user.migrated_from}</p>` : ''}
                        </div>
                    </div>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span style="padding: 4px 8px; background: ${roleColor}; color: white; border-radius: 12px; font-size: 0.8rem;">
                        ${roleName}
                    </span>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span style="padding: 4px 8px; background: ${statusColor}; color: white; border-radius: 12px; font-size: 0.8rem;">
                        ${statusText}
                    </span>
                </td>
                <td style="padding: 15px; text-align: center; color: #666; font-size: 0.9em;">
                    ${lastLogin}
                </td>
                <td style="padding: 15px; text-align: center;">
                    <div style="display: flex; gap: 5px; justify-content: center;">
                        <button onclick="viewUserDetails(${user.id}, '${user.user_type || 'user'}')" style="padding: 6px 10px; background: #17a2b8; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${!isAdmin ? `
                            <button onclick="editUser(${user.id})" style="padding: 6px 10px; background: #ffc107; color: #333; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="toggleUserStatus(${user.id})" style="padding: 6px 10px; background: ${isActive ? '#dc3545' : '#28a745'}; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="${isActive ? 'تعطيل' : 'تفعيل'}">
                                <i class="fas fa-${isActive ? 'ban' : 'check'}"></i>
                            </button>
                            <button onclick="deleteUser(${user.id})" style="padding: 6px 10px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : `
                            <span style="padding: 6px 10px; background: #6c757d; color: white; border-radius: 6px; font-size: 0.8rem; opacity: 0.7;" title="المديرون محميون من التعديل">
                                <i class="fas fa-lock"></i> محمي
                            </span>
                        `}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Render users management interface (fallback)
 */
function renderUsersInterface(container, data) {
    console.log('🎨 رسم واجهة إدارة المستخدمين...');

    if (!data || !data.users) {
        console.error('❌ بيانات غير صحيحة للرسم');
        showErrorState(container, 'بيانات المستخدمين غير صحيحة');
        return;
    }

    const stats = data.statistics || {};
    const users = data.users || [];
    const pagination = data.pagination || {};
    const roles = data.roles || [];

    const html = `
        <div class="users-management-container">
            <!-- Header -->
            <div class="users-management-header">
                <h2><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                <p>إدارة وتنظيم جميع مستخدمي النظام والصلاحيات</p>
                <div class="header-actions">
                    <button class="header-btn primary" onclick="showCreateUserModal()">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </button>
                    <button class="header-btn secondary" onclick="exportUsers()">
                        <i class="fas fa-download"></i> تصدير المستخدمين
                    </button>
                    <button class="header-btn secondary" onclick="loadUsersManagementContent()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="users-stats">
                <div class="stat-card">
                    <div class="stat-icon total">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="stat-number">${stats.total_users || 0}</h3>
                    <p class="stat-label">إجمالي المستخدمين</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon active">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3 class="stat-number">${stats.active_users || 0}</h3>
                    <p class="stat-label">المستخدمون النشطون</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon verified">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h3 class="stat-number">${stats.verified_users || 0}</h3>
                    <p class="stat-label">المستخدمون المتحققون</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon today">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3 class="stat-number">${stats.today_registrations || 0}</h3>
                    <p class="stat-label">تسجيلات اليوم</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon month">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="stat-number">${stats.month_registrations || 0}</h3>
                    <p class="stat-label">تسجيلات الشهر</p>
                </div>
            </div>

            <!-- Filters -->
            <div class="users-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label class="filter-label">البحث</label>
                        <input type="text" class="filter-input" id="searchInput"
                               placeholder="البحث بالاسم، البريد الإلكتروني، أو اسم المستخدم..."
                               value="${data.filters?.search || ''}"
                               onkeyup="handleSearchKeyup(event)">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">الدور</label>
                        <select class="filter-input filter-select" id="roleFilter" onchange="applyFilters()">
                            <option value="">جميع الأدوار</option>
                            ${roles.map(role => `
                                <option value="${role.name}" ${data.filters?.role === role.name ? 'selected' : ''}>
                                    ${role.display_name_ar} (${role.users_count})
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">الحالة</label>
                        <select class="filter-input filter-select" id="statusFilter" onchange="applyFilters()">
                            <option value="">جميع الحالات</option>
                            <option value="active" ${data.filters?.status === 'active' ? 'selected' : ''}>نشط</option>
                            <option value="inactive" ${data.filters?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                            <option value="verified" ${data.filters?.status === 'verified' ? 'selected' : ''}>متحقق</option>
                            <option value="unverified" ${data.filters?.status === 'unverified' ? 'selected' : ''}>غير متحقق</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="filter-btn" onclick="applyFilters()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <button class="filter-btn clear" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح
                        </button>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="users-table-container">
                <div class="users-table-header">
                    <h3 class="users-table-title">
                        <i class="fas fa-list"></i>
                        قائمة المستخدمين
                        <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">${pagination.total || 0}</span>
                    </h3>
                    <div class="users-table-actions">
                        <button class="btn primary" onclick="showCreateUserModal()">
                            <i class="fas fa-plus"></i> إضافة مستخدم
                        </button>
                    </div>
                </div>

                <table class="users-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>الأدوار</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => renderUserRow(user)).join('')}
                    </tbody>
                </table>

                ${users.length === 0 ? `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p style="font-size: 1.1rem; margin: 0;">لا توجد مستخدمين للعرض</p>
                        <p style="margin: 5px 0 0 0; font-size: 0.9rem;">جرب تغيير معايير البحث أو الفلترة</p>
                    </div>
                ` : ''}
            </div>

            <!-- Pagination -->
            ${renderPagination(pagination)}

            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة المستخدمين بنجاح!</strong>
                <br>تم عرض ${users.length} مستخدم من أصل ${pagination.total || 0} مستخدم.
            </div>
        </div>

        <!-- User Modal -->
        <div id="userModal" class="modal-overlay">
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title" id="modalTitle">إضافة مستخدم جديد</h3>
                    <button class="modal-close" onclick="closeUserModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="userForm" class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">اسم المستخدم</label>
                            <input type="text" class="form-input" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">البريد الإلكتروني</label>
                            <input type="email" class="form-input" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">الاسم الأول</label>
                            <input type="text" class="form-input" id="firstName" name="first_name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">الاسم الأخير</label>
                            <input type="text" class="form-input" id="lastName" name="last_name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-input" id="phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">كلمة المرور</label>
                            <input type="password" class="form-input" id="password" name="password" required>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">نبذة شخصية</label>
                            <textarea class="form-input form-textarea" id="bio" name="bio" placeholder="نبذة مختصرة عن المستخدم..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-checkbox">
                                <input type="checkbox" id="isActive" name="is_active" checked>
                                <span>مستخدم نشط</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="form-checkbox">
                                <input type="checkbox" id="isVerified" name="is_verified">
                                <span>مستخدم متحقق</span>
                            </label>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">الأدوار</label>
                            <div id="rolesContainer">
                                ${roles.map(role => `
                                    <label class="form-checkbox" style="margin-bottom: 8px;">
                                        <input type="checkbox" name="roles[]" value="${role.id}">
                                        <span style="color: ${role.color};">
                                            <i class="${role.icon}"></i> ${role.display_name_ar}
                                        </span>
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn primary" onclick="saveUser()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button class="btn secondary" onclick="closeUserModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        </div>
    `;

    console.log('✅ تم إنشاء HTML للواجهة');
    container.innerHTML = html;

    console.log('✅ تم تحديث محتوى الحاوي بنجاح');
}

/**
 * Render user row
 */
function renderUserRow(user) {
    const avatar = user.avatar || getInitials(user.full_name || user.username);
    const roles = user.roles_ar ? user.roles_ar.split(', ') : [];
    const roleColors = user.role_colors ? user.role_colors.split(', ') : [];
    const roleIcons = user.role_icons ? user.role_icons.split(', ') : [];

    const activeStatus = user.is_active == 1 ? 'active' : 'inactive';
    const verifiedStatus = user.is_verified == 1 ? 'verified' : 'unverified';

    const lastLogin = user.last_login_at ?
        new Date(user.last_login_at).toLocaleDateString('ar-SA') : 'لم يسجل دخول';

    const createdAt = new Date(user.created_at).toLocaleDateString('ar-SA');

    return `
        <tr>
            <td>
                <div class="user-info">
                    <div class="user-avatar">${avatar}</div>
                    <div class="user-details">
                        <p class="user-name">${user.full_name || user.username}</p>
                        <p class="user-email">${user.email}</p>
                    </div>
                </div>
            </td>
            <td>
                <div class="user-roles">
                    ${roles.map((role, index) => `
                        <span class="role-badge" style="background: ${roleColors[index] || '#667eea'};">
                            <i class="${roleIcons[index] || 'fas fa-user'}"></i>
                            ${role}
                        </span>
                    `).join('')}
                </div>
            </td>
            <td>
                <div style="display: flex; flex-direction: column; gap: 4px;">
                    <span class="status-badge ${activeStatus}">
                        ${activeStatus === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                    <span class="status-badge ${verifiedStatus}">
                        ${verifiedStatus === 'verified' ? 'متحقق' : 'غير متحقق'}
                    </span>
                </div>
            </td>
            <td>${createdAt}</td>
            <td>${lastLogin}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewUser(${user.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editUser(${user.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteUser(${user.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * Render pagination
 */
function renderPagination(pagination) {
    if (!pagination || pagination.total_pages <= 1) {
        return '';
    }

    const currentPage = pagination.current_page;
    const totalPages = pagination.total_pages;
    const total = pagination.total;
    const perPage = pagination.per_page;

    const startItem = ((currentPage - 1) * perPage) + 1;
    const endItem = Math.min(currentPage * perPage, total);

    let paginationHtml = `
        <div class="pagination-container">
            <div class="pagination-info">
                عرض ${startItem} إلى ${endItem} من أصل ${total} مستخدم
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" onclick="changePage(1)" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-angle-double-right"></i>
                </button>
                <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-angle-right"></i>
                </button>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                ${i}
            </button>
        `;
    }

    paginationHtml += `
                <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-angle-left"></i>
                </button>
                <button class="pagination-btn" onclick="changePage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-angle-double-left"></i>
                </button>
            </div>
        </div>
    `;

    return paginationHtml;
}

/**
 * Get user initials for avatar
 */
function getInitials(name) {
    if (!name) return '?';
    const words = name.trim().split(' ');
    if (words.length >= 2) {
        return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name[0].toUpperCase();
}

/**
 * Handle search keyup
 */
function handleSearchKeyup(event) {
    if (event.key === 'Enter') {
        applyFilters();
    }
}

/**
 * Apply filters
 */
function applyFilters() {
    console.log('🔍 تطبيق الفلاتر...');

    currentFilters.search = document.getElementById('searchInput').value;
    currentFilters.role = document.getElementById('roleFilter').value;
    currentFilters.status = document.getElementById('statusFilter').value;
    currentPage = 1;

    loadUsersWithFilters();
}

/**
 * Clear filters
 */
function clearFilters() {
    console.log('🗑️ مسح الفلاتر...');

    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';

    currentFilters = { search: '', role: '', status: '' };
    currentPage = 1;

    loadUsersWithFilters();
}

/**
 * Load users with current filters
 */
function loadUsersWithFilters() {
    const container = document.getElementById('usersManagementContent');
    showLoadingState(container);

    fetchUsersData(currentPage, 20, currentFilters.search, currentFilters.role, currentFilters.status)
        .then(data => {
            if (data.success) {
                usersData = data.data;
                renderUsersInterface(container, data.data);
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            showErrorState(container, error.message);
        });
}

/**
 * Change page
 */
function changePage(page) {
    console.log('📄 تغيير الصفحة إلى:', page);
    currentPage = page;
    loadUsersWithFilters();
}

// Placeholder functions for user actions
function showCreateUserModal() {
    console.log('➕ عرض نموذج إضافة مستخدم...');
    document.getElementById('userModal').classList.add('active');
}

function closeUserModal() {
    console.log('❌ إغلاق نموذج المستخدم...');
    document.getElementById('userModal').classList.remove('active');
}

function saveUser() {
    console.log('💾 حفظ المستخدم...');
    alert('حفظ المستخدم - قيد التطوير');
}

function viewUser(userId) {
    console.log('👁️ عرض المستخدم:', userId);
    alert(`عرض المستخدم ${userId} - قيد التطوير`);
}

function editUser(userId) {
    console.log('✏️ تعديل المستخدم:', userId);
    alert(`تعديل المستخدم ${userId} - قيد التطوير`);
}

function deleteUser(userId) {
    console.log('🗑️ حذف المستخدم:', userId);
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        alert(`حذف المستخدم ${userId} - قيد التطوير`);
    }
}

function exportUsers() {
    console.log('📤 تصدير المستخدمين...');
    alert('تصدير المستخدمين - قيد التطوير');
}

// User Management Functions

/**
 * Show add user modal
 */
function showAddUserModal() {
    editingUserId = null;
    const modalContainer = document.getElementById('userModal');
    modalContainer.innerHTML = createUserModal(false);
    modalContainer.style.display = 'block';
}

/**
 * Close user modal
 */
function closeUserModal() {
    const modalContainer = document.getElementById('userModal');
    modalContainer.style.display = 'none';
    modalContainer.innerHTML = '';
    editingUserId = null;
}

/**
 * Create user modal HTML
 */
function createUserModal(isEdit = false, userData = null) {
    const modalTitle = isEdit ? 'تعديل المستخدم' : 'إضافة مستخدم جديد';
    const saveButtonText = isEdit ? 'تحديث' : 'حفظ';
    const roles = window.allRolesData || [];

    return `
        <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.2); max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;">${modalTitle}</h3>
                    <button onclick="closeUserModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666; padding: 0; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div style="padding: 20px;">
                    <form id="userForm" style="display: grid; gap: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الاسم الأول *</label>
                                <input type="text" id="firstName" name="first_name" required value="${userData?.first_name || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الاسم الأخير *</label>
                                <input type="text" id="lastName" name="last_name" required value="${userData?.last_name || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" required value="${userData?.email || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone" value="${userData?.phone || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الدور *</label>
                                <select id="roleId" name="role_id" required style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                                    <option value="">اختر الدور</option>
                                    ${roles.map(role => `<option value="${role.id}" ${userData?.role_id == role.id ? 'selected' : ''}>${role.display_name_ar}</option>`).join('')}
                                </select>
                            </div>
                        </div>

                        ${!isEdit ? `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">كلمة المرور *</label>
                                <input type="password" id="password" name="password" required style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">تأكيد كلمة المرور *</label>
                                <input type="password" id="confirmPassword" name="confirm_password" required style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                        </div>
                        ` : ''}

                        <div>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="isActive" name="is_active" ${userData?.is_active == 1 ? 'checked' : 'checked'} style="width: 18px; height: 18px; accent-color: #667eea;">
                                <span>مستخدم نشط</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div style="padding: 20px; border-top: 1px solid #e0e0e0; display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="saveUser()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-save"></i> ${saveButtonText}
                    </button>
                    <button onclick="closeUserModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Save user (add or edit)
 */
async function saveUser() {
    const form = document.getElementById('userForm');
    if (!form) {
        alert('خطأ: لم يتم العثور على النموذج');
        return;
    }

    // Validate required fields
    const firstName = document.getElementById('firstName').value.trim();
    const lastName = document.getElementById('lastName').value.trim();
    const email = document.getElementById('email').value.trim();
    const roleId = document.getElementById('roleId').value;

    if (!firstName || !lastName || !email || !roleId) {
        alert('جميع الحقول المطلوبة يجب ملؤها');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('صيغة البريد الإلكتروني غير صحيحة');
        return;
    }

    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Convert checkbox
    data.is_active = document.getElementById('isActive').checked ? 1 : 0;

    // Validate passwords for new users
    if (!editingUserId) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!password || password.length < 6) {
            alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        if (password !== confirmPassword) {
            alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
            return;
        }
    }

    console.log('💾 بيانات المستخدم للحفظ:', data);

    try {
        const url = editingUserId ?
            `php/users_management.php?action=update&id=${editingUserId}` :
            'php/users_management.php?action=create';

        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            const message = editingUserId ? 'تم تحديث المستخدم بنجاح!' : 'تم إضافة المستخدم بنجاح!';
            alert(message);
            closeUserModal();
            loadUsersManagementContent();
        } else {
            alert('خطأ: ' + (result.message || 'حدث خطأ غير معروف'));
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Edit user
 */
async function editUser(id) {
    try {
        const response = await fetch(`php/users_management.php?action=get_by_id&id=${id}`);
        const result = await response.json();

        if (result.success && result.data) {
            const user = result.data.user || result.data;
            editingUserId = id;

            const modalContainer = document.getElementById('userModal');
            modalContainer.innerHTML = createUserModal(true, user);
            modalContainer.style.display = 'block';
        } else {
            alert('خطأ في جلب بيانات المستخدم');
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Toggle user status
 */
async function toggleUserStatus(id) {
    if (!confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')) {
        return;
    }

    try {
        const response = await fetch(`php/users_management.php?action=toggle_status&id=${id}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            loadUsersManagementContent();
        } else {
            alert('خطأ: ' + result.message);
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Delete user
 */
async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        const response = await fetch(`php/users_management.php?action=delete&id=${id}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            alert('تم حذف المستخدم بنجاح!');
            loadUsersManagementContent();
        } else {
            alert('خطأ: ' + result.message);
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * View user details
 */
function viewUserDetails(id, userType = 'user') {
    // Open user details in a new window or modal with user type
    window.open(`users-management-standalone.html?user=${id}&type=${userType}`, '_blank');
}

/**
 * Apply users filters
 */
function applyUsersFilters() {
    const search = document.getElementById('userSearch').value;
    const role = document.getElementById('roleFilter').value;
    const status = document.getElementById('statusFilter').value;

    currentFilters = { search, role, status };
    currentPage = 1;

    loadUsersManagementContent();
}

/**
 * Show roles management
 */
function showRolesManagement() {
    console.log('🛡️ بدء تحميل إدارة الأدوار...');

    // Try multiple possible container IDs
    let container = document.getElementById('usersManagementContent') ||
                   document.getElementById('rolesManagementContent') ||
                   document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي المحتوى');
        console.log('Available containers:',
            Array.from(document.querySelectorAll('[id*="Content"]')).map(el => el.id));
        return;
    }

    console.log('✅ تم العثور على الحاوي:', container.id);

    // Show loading state
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <p style="color: #666; font-size: 1.1rem;">جاري تحميل إدارة الأدوار...</p>
        </div>
    `;

    // Load roles management interface
    setTimeout(() => {
        loadRolesManagementInterface();
    }, 500);
}

/**
 * Load roles management interface
 */
function loadRolesManagementInterface() {
    // Try multiple possible container IDs
    const container = document.getElementById('usersManagementContent') ||
                     document.getElementById('rolesManagementContent') ||
                     document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي واجهة إدارة الأدوار');
        return;
    }

    console.log('✅ تحميل واجهة إدارة الأدوار في:', container.id);

    container.innerHTML = `
        <div class="roles-management-container">
            <!-- Header -->
            <div class="roles-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
                <h1 style="margin: 0 0 10px 0; font-size: 2rem;">
                    <i class="fas fa-user-shield"></i> إدارة الأدوار والصلاحيات
                </h1>
                <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">إدارة أدوار المستخدمين وتحديد الصلاحيات</p>
                <div style="margin-top: 20px;">
                    <button onclick="showAddRoleModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-plus"></i> إضافة دور جديد
                    </button>
                    <button onclick="loadUsersManagementContent()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-arrow-left"></i> العودة للمستخدمين
                    </button>
                </div>
            </div>

            <!-- Roles List -->
            <div class="roles-list-container">
                <div id="rolesList" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px;">
                    <!-- Roles will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Add/Edit Role Modal -->
        <div id="roleModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
            <div class="modal-content" style="background-color: #fefefe; margin: 5% auto; padding: 0; border-radius: 15px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-user-shield"></i>
                        <span id="roleModalTitle">إضافة دور جديد</span>
                    </h3>
                    <button onclick="closeRoleModal()" style="position: absolute; top: 15px; left: 15px; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" style="padding: 30px;">
                    <form id="roleForm">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">اسم الدور (بالإنجليزية)</label>
                            <input type="text" id="roleName" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">اسم الدور (بالعربية)</label>
                            <input type="text" id="roleDisplayNameAr" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">اسم الدور (بالإنجليزية)</label>
                            <input type="text" id="roleDisplayNameEn" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">الوصف</label>
                            <textarea id="roleDescription" rows="3" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 1rem; resize: vertical;"></textarea>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">مستوى الدور (1-100)</label>
                            <input type="number" id="roleLevel" min="1" max="100" value="20" required style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 1rem;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">الصلاحيات</label>
                            <div id="permissionsContainer" style="border: 2px solid #ddd; border-radius: 8px; padding: 15px; max-height: 200px; overflow-y: auto;">
                                <!-- Permissions checkboxes will be loaded here -->
                            </div>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" id="roleIsActive" checked style="transform: scale(1.2);">
                                <span style="font-weight: bold; color: #333;">الدور نشط</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="padding: 20px 30px; border-top: 1px solid #eee; display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="closeRoleModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button onclick="saveRole()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        </div>
    `;

    // Load roles data
    loadRolesData();
}

/**
 * Load roles data from API
 */
async function loadRolesData() {
    try {
        console.log('📡 جاري تحميل بيانات الأدوار...');

        const response = await fetch('php/role-management-api.php?action=roles');
        const data = await response.json();

        if (data.success) {
            displayRoles(data.roles || []);
        } else {
            throw new Error(data.message || 'فشل في تحميل الأدوار');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الأدوار:', error);
        document.getElementById('rolesList').innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <h3>خطأ في تحميل الأدوار</h3>
                <p>${error.message}</p>
                <button onclick="loadRolesData()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Display roles in the interface
 */
function displayRoles(roles) {
    const container = document.getElementById('rolesList');

    if (!roles || roles.length === 0) {
        container.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>لا توجد أدوار</h3>
                <p>لم يتم العثور على أي أدوار في النظام</p>
                <button onclick="showAddRoleModal()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-plus"></i> إضافة دور جديد
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = roles.map(role => `
        <div class="role-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee; transition: all 0.3s ease;">
            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 15px;">
                <div style="flex: 1;">
                    <h3 style="margin: 0 0 5px 0; color: #333; font-size: 1.3rem;">
                        <i class="fas fa-user-shield" style="color: #667eea; margin-left: 8px;"></i>
                        ${role.display_name_ar}
                    </h3>
                    <p style="margin: 0; color: #666; font-size: 0.9rem;">${role.display_name_en}</p>
                </div>
                <div style="display: flex; gap: 5px;">
                    <button onclick="editRole(${role.id})" style="padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 0.8rem;">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteRole(${role.id})" style="padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 0.8rem;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div style="margin-bottom: 15px;">
                <p style="margin: 0 0 8px 0; color: #666; font-size: 0.9rem;">${role.description || 'لا يوجد وصف'}</p>
                <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                    <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">
                        <i class="fas fa-layer-group"></i> مستوى ${role.level}
                    </span>
                    <span style="background: ${role.is_active ? '#28a745' : '#dc3545'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">
                        <i class="fas fa-${role.is_active ? 'check' : 'times'}"></i> ${role.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
            </div>

            <div style="border-top: 1px solid #eee; padding-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #333; font-size: 1rem;">الصلاحيات:</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                    ${role.permissions ? JSON.parse(role.permissions).map(perm => `
                        <span style="background: #f8f9fa; color: #495057; padding: 3px 8px; border-radius: 8px; font-size: 0.75rem; border: 1px solid #dee2e6;">
                            ${perm}
                        </span>
                    `).join('') : '<span style="color: #999; font-size: 0.8rem;">لا توجد صلاحيات</span>'}
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Show add role modal
 */
function showAddRoleModal() {
    document.getElementById('roleModalTitle').textContent = 'إضافة دور جديد';
    document.getElementById('roleForm').reset();
    document.getElementById('roleIsActive').checked = true;
    loadPermissionsOptions();
    document.getElementById('roleModal').style.display = 'block';
    currentEditingRoleId = null;
}

/**
 * Close role modal
 */
function closeRoleModal() {
    document.getElementById('roleModal').style.display = 'none';
    currentEditingRoleId = null;
}

/**
 * Load permissions options
 */
function loadPermissionsOptions() {
    const container = document.getElementById('permissionsContainer');

    const availablePermissions = [
        { id: 'users', name: 'إدارة المستخدمين' },
        { id: 'products', name: 'إدارة المنتجات' },
        { id: 'orders', name: 'إدارة الطلبات' },
        { id: 'settings', name: 'إدارة الإعدادات' },
        { id: 'reports', name: 'عرض التقارير' },
        { id: 'landing_pages', name: 'إدارة صفحات الهبوط' },
        { id: 'stores', name: 'إدارة المتاجر' },
        { id: 'roles', name: 'إدارة الأدوار' },
        { id: 'all', name: 'جميع الصلاحيات' }
    ];

    container.innerHTML = availablePermissions.map(perm => `
        <label style="display: flex; align-items: center; gap: 10px; padding: 8px; cursor: pointer; border-radius: 5px; transition: background 0.2s;">
            <input type="checkbox" name="permissions" value="${perm.id}" style="transform: scale(1.1);">
            <span style="font-size: 0.9rem;">${perm.name}</span>
        </label>
    `).join('');
}

/**
 * Save role (add or edit)
 */
async function saveRole() {
    try {
        const formData = {
            name: document.getElementById('roleName').value.trim(),
            display_name_ar: document.getElementById('roleDisplayNameAr').value.trim(),
            display_name_en: document.getElementById('roleDisplayNameEn').value.trim(),
            description: document.getElementById('roleDescription').value.trim(),
            level: parseInt(document.getElementById('roleLevel').value),
            is_active: document.getElementById('roleIsActive').checked ? 1 : 0,
            permissions: Array.from(document.querySelectorAll('input[name="permissions"]:checked')).map(cb => cb.value)
        };

        // Validation
        if (!formData.name || !formData.display_name_ar || !formData.display_name_en) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (formData.level < 1 || formData.level > 100) {
            alert('مستوى الدور يجب أن يكون بين 1 و 100');
            return;
        }

        const url = currentEditingRoleId
            ? `php/role-management-api.php?action=update&id=${currentEditingRoleId}`
            : 'php/role-management-api.php?action=create';

        const method = currentEditingRoleId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            alert(currentEditingRoleId ? 'تم تحديث الدور بنجاح!' : 'تم إضافة الدور بنجاح!');
            closeRoleModal();
            loadRolesData();
        } else {
            alert('خطأ: ' + (result.message || 'حدث خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ الدور:', error);
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Edit role
 */
async function editRole(roleId) {
    try {
        const response = await fetch(`../php/api/roles.php?action=get&id=${roleId}`);
        const result = await response.json();

        if (result.success && result.role) {
            const role = result.role;

            document.getElementById('roleModalTitle').textContent = 'تعديل الدور';
            document.getElementById('roleName').value = role.name;
            document.getElementById('roleDisplayNameAr').value = role.display_name_ar;
            document.getElementById('roleDisplayNameEn').value = role.display_name_en;
            document.getElementById('roleDescription').value = role.description || '';
            document.getElementById('roleLevel').value = role.level;
            document.getElementById('roleIsActive').checked = role.is_active == 1;

            loadPermissionsOptions();

            // Set permissions
            if (role.permissions) {
                const permissions = JSON.parse(role.permissions);
                permissions.forEach(perm => {
                    const checkbox = document.querySelector(`input[name="permissions"][value="${perm}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            currentEditingRoleId = roleId;
            document.getElementById('roleModal').style.display = 'block';
        } else {
            alert('خطأ في تحميل بيانات الدور');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الدور:', error);
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Delete role
 */
async function deleteRole(roleId) {
    if (!confirm('هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        const response = await fetch(`php/role-management-api.php?action=delete&id=${roleId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            alert('تم حذف الدور بنجاح!');
            loadRolesData();
        } else {
            alert('خطأ: ' + (result.message || 'فشل في حذف الدور'));
        }
    } catch (error) {
        console.error('❌ خطأ في حذف الدور:', error);
        alert('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Load roles management content (called from admin panel)
 */
function loadRolesManagementContent() {
    console.log('🛡️ تحميل محتوى إدارة الأدوار من لوحة التحكم...');
    showRolesManagement();
}

// Global variables for role management
let currentEditingRoleId = null;

// Make functions globally available
window.loadUsersManagementContent = loadUsersManagementContent;
window.showAddUserModal = showAddUserModal;
window.closeUserModal = closeUserModal;
window.saveUser = saveUser;
window.editUser = editUser;
window.toggleUserStatus = toggleUserStatus;
window.deleteUser = deleteUser;
window.viewUserDetails = viewUserDetails;
window.applyUsersFilters = applyUsersFilters;
window.showRolesManagement = showRolesManagement;
window.loadRolesManagementContent = loadRolesManagementContent;
window.showAddRoleModal = showAddRoleModal;
window.closeRoleModal = closeRoleModal;
window.loadRolesData = loadRolesData;
window.saveRole = saveRole;
window.editRole = editRole;
window.deleteRole = deleteRole;

console.log('✅ تم تحميل ملف users-management.js بنجاح');
console.log('🎯 الدالة الرئيسية متاحة:', typeof loadUsersManagementContent);
