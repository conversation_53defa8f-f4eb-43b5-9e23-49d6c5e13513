<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التخطيط - قائمة إعدادات الإدارة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/admin-settings-menu-enhanced.css">
    <link rel="stylesheet" href="css/admin-settings-layout-fix.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .test-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 0;
            min-height: 400px;
        }
        
        .test-nav {
            padding: 0;
        }
        
        .test-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        
        .test-nav ul li {
            width: 100%;
            display: block;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .test-instructions {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .test-instructions h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .test-instructions ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        
        .test-instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-tools"></i> اختبار إصلاح التخطيط</h1>
            <p>قائمة إعدادات الإدارة - الروابط أسفل الرأس</p>
        </div>
        
        <div class="test-instructions">
            <h3><i class="fas fa-info-circle"></i> تعليمات الاختبار:</h3>
            <ul>
                <li>اضغط على "إعدادات الإدارة" لفتح القائمة</li>
                <li>تأكد من ظهور الروابط أسفل الرأس وليس بجانبه</li>
                <li>اختبر النقر على الروابط الفرعية</li>
                <li>تأكد من التمييز النشط للعناصر</li>
            </ul>
        </div>
        
        <div class="status-info">
            <i class="fas fa-arrow-down"></i> اضغط على القائمة أدناه للاختبار
        </div>
        
        <div class="test-sidebar">
            <nav class="test-nav admin-nav">
                <ul>
                    <!-- Admin Settings Collapsible Menu -->
                    <li class="admin-settings-menu">
                        <div class="admin-settings-header" onclick="toggleAdminSettings()">
                            <div class="admin-settings-header-content">
                                <i class="fas fa-cogs"></i>
                                <span>إعدادات الإدارة</span>
                            </div>
                            <i class="fas fa-chevron-down admin-settings-arrow"></i>
                        </div>
                        <ul class="admin-settings-submenu">
                            <li data-section="generalSettings" onclick="testMenuClick('الإعدادات العامة')">
                                <i class="fas fa-cog"></i>
                                <span>الإعدادات العامة</span>
                            </li>
                            <li data-section="paymentSettings" onclick="testMenuClick('إعدادات الدفع')">
                                <i class="fas fa-credit-card"></i>
                                <span>إعدادات الدفع</span>
                            </li>
                            <li data-section="categories" onclick="testMenuClick('إدارة الفئات')">
                                <i class="fas fa-tags"></i>
                                <span>إدارة الفئات</span>
                            </li>
                            <li data-section="storeSettings" onclick="testMenuClick('إعدادات المتجر')">
                                <i class="fas fa-store"></i>
                                <span>إعدادات المتجر</span>
                            </li>
                            <li data-section="securitySettings" onclick="testMenuClick('الأمان')">
                                <i class="fas fa-shield-alt"></i>
                                <span>الأمان</span>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        
        <div id="testResult" class="status-info" style="display: none;">
            <i class="fas fa-check-circle"></i> تم النقر على: <span id="clickedItem"></span>
        </div>
    </div>

    <script>
        // Admin Settings Menu Functions
        function toggleAdminSettings() {
            const menu = document.querySelector('.admin-settings-menu');
            const submenu = document.querySelector('.admin-settings-submenu');
            
            if (menu.classList.contains('expanded')) {
                menu.classList.remove('expanded');
                submenu.style.maxHeight = '0';
                console.log('Menu collapsed');
                
                // Update status
                const statusDiv = document.querySelector('.status-info');
                statusDiv.innerHTML = '<i class="fas fa-info-circle"></i> تم إغلاق القائمة - اضغط مرة أخرى للفتح';
            } else {
                menu.classList.add('expanded');
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                console.log('Menu expanded');
                
                // Update status
                const statusDiv = document.querySelector('.status-info');
                statusDiv.innerHTML = '<i class="fas fa-check-circle"></i> تم فتح القائمة - الروابط تظهر أسفل الرأس ✅';
                statusDiv.style.background = '#d4edda';
                statusDiv.style.color = '#155724';
                statusDiv.style.borderColor = '#c3e6cb';
            }
        }
        
        function testMenuClick(itemName) {
            // Remove active class from all submenu items
            document.querySelectorAll('.admin-settings-submenu li').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            event.target.closest('li').classList.add('active');
            
            // Show result
            const resultDiv = document.getElementById('testResult');
            const clickedItem = document.getElementById('clickedItem');
            
            clickedItem.textContent = itemName;
            resultDiv.style.display = 'block';
            resultDiv.style.background = '#d4edda';
            resultDiv.style.color = '#155724';
            resultDiv.style.borderColor = '#c3e6cb';
            resultDiv.innerHTML = `<i class="fas fa-check-circle"></i> تم النقر على: ${itemName} ✅`;
            
            console.log('Clicked on:', itemName);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Layout Fix Test initialized');
            
            // Add click handlers for submenu items
            const submenuItems = document.querySelectorAll('.admin-settings-submenu li');
            submenuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // Remove active class from all submenu items
                    submenuItems.forEach(i => i.classList.remove('active'));
                    // Add active class to clicked item
                    this.classList.add('active');
                });
            });
            
            // Test auto-expand after 2 seconds
            setTimeout(() => {
                const statusDiv = document.querySelector('.status-info');
                statusDiv.innerHTML = '<i class="fas fa-hand-pointer"></i> جاهز للاختبار - اضغط على "إعدادات الإدارة"';
                statusDiv.style.background = '#fff3cd';
                statusDiv.style.color = '#856404';
                statusDiv.style.borderColor = '#ffeaa7';
            }, 1000);
        });
    </script>
</body>
</html>
