<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظيفة إدارة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #testResults { margin-top: 20px; }
        #rolesManagementContent { border: 2px solid #ddd; border-radius: 10px; padding: 20px; margin-top: 20px; min-height: 200px; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار وظيفة إدارة الأدوار</h1>
        <p>اختبار تحميل وعمل وظائف إدارة الأدوار</p>

        <div class="info">
            <h3>📋 الاختبارات المتاحة:</h3>
            <button class="test-button" onclick="testFunctionAvailability()">
                <i class="fas fa-search"></i> اختبار توفر الوظائف
            </button>
            <button class="test-button" onclick="testLoadRolesManagementContent()">
                <i class="fas fa-play"></i> اختبار تحميل إدارة الأدوار
            </button>
            <button class="test-button" onclick="testShowRolesManagement()">
                <i class="fas fa-cog"></i> اختبار عرض إدارة الأدوار
            </button>
            <button class="test-button" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
        </div>

        <div id="testResults"></div>

        <!-- Container for role management content -->
        <div id="rolesManagementContent">
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>منطقة اختبار إدارة الأدوار</h3>
                <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
            </div>
        </div>
    </div>

    <!-- Load the users management script -->
    <script src="admin/js/users-management.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('rolesManagementContent').innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-user-shield" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3>منطقة اختبار إدارة الأدوار</h3>
                    <p>سيتم تحميل محتوى إدارة الأدوار هنا</p>
                </div>
            `;
        }

        function testFunctionAvailability() {
            addResult('<h3>🔍 اختبار توفر الوظائف:</h3>');
            
            const functions = [
                'loadRolesManagementContent',
                'showRolesManagement', 
                'loadRolesManagementInterface',
                'loadRolesData',
                'showAddRoleModal',
                'closeRoleModal',
                'saveRole',
                'editRole',
                'deleteRole'
            ];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} - متوفرة`, 'success');
                } else {
                    addResult(`❌ ${funcName} - غير متوفرة`, 'error');
                }
            });
        }

        function testLoadRolesManagementContent() {
            addResult('<h3>🚀 اختبار تحميل إدارة الأدوار:</h3>');
            
            try {
                if (typeof loadRolesManagementContent === 'function') {
                    addResult('✅ الوظيفة loadRolesManagementContent متوفرة', 'success');
                    addResult('🔄 محاولة تحميل المحتوى...', 'info');
                    
                    loadRolesManagementContent();
                    
                    setTimeout(() => {
                        const content = document.getElementById('rolesManagementContent').innerHTML;
                        if (content.includes('إدارة الأدوار والصلاحيات')) {
                            addResult('✅ تم تحميل محتوى إدارة الأدوار بنجاح!', 'success');
                        } else {
                            addResult('⚠️ تم استدعاء الوظيفة لكن المحتوى لم يتغير', 'error');
                        }
                    }, 1000);
                } else {
                    addResult('❌ الوظيفة loadRolesManagementContent غير متوفرة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل إدارة الأدوار: ${error.message}`, 'error');
            }
        }

        function testShowRolesManagement() {
            addResult('<h3>🛡️ اختبار عرض إدارة الأدوار:</h3>');
            
            try {
                if (typeof showRolesManagement === 'function') {
                    addResult('✅ الوظيفة showRolesManagement متوفرة', 'success');
                    addResult('🔄 محاولة عرض إدارة الأدوار...', 'info');
                    
                    showRolesManagement();
                    
                    setTimeout(() => {
                        const content = document.getElementById('rolesManagementContent').innerHTML;
                        if (content.includes('إدارة الأدوار والصلاحيات')) {
                            addResult('✅ تم عرض إدارة الأدوار بنجاح!', 'success');
                        } else {
                            addResult('⚠️ تم استدعاء الوظيفة لكن المحتوى لم يتغير', 'error');
                        }
                    }, 1000);
                } else {
                    addResult('❌ الوظيفة showRolesManagement غير متوفرة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في عرض إدارة الأدوار: ${error.message}`, 'error');
            }
        }

        // Test on page load
        window.addEventListener('load', function() {
            addResult('<h3>🔄 اختبار تلقائي عند تحميل الصفحة:</h3>');
            
            if (typeof loadUsersManagementContent === 'function') {
                addResult('✅ تم تحميل ملف users-management.js بنجاح', 'success');
            } else {
                addResult('❌ فشل في تحميل ملف users-management.js', 'error');
            }
            
            // Check console for any errors
            const originalConsoleError = console.error;
            const errors = [];
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalConsoleError.apply(console, args);
            };
            
            setTimeout(() => {
                if (errors.length > 0) {
                    addResult(`⚠️ أخطاء في وحدة التحكم: ${errors.join(', ')}`, 'error');
                } else {
                    addResult('✅ لا توجد أخطاء في وحدة التحكم', 'success');
                }
            }, 500);
        });
    </script>
</body>
</html>
