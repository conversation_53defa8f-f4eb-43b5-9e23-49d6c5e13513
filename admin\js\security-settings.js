/**
 * Security Settings Management
 * Handles all security-related settings functionality
 */

// Import core settings functionality
document.addEventListener('DOMContentLoaded', () => {
    initializeSettings();
    initializeSecuritySettings();
});

// Default security settings
let securitySettings = {
    authentication: {
        requireStrongPassword: true,
        enableTwoFactor: false,
        sessionTimeout: 30,
        maxLoginAttempts: 5
    },
    accessControl: {
        enableIPWhitelist: false,
        ipWhitelist: [],
        enableGeoBlocking: false,
        blockedCountries: []
    },
    dataProtection: {
        enableDataEncryption: true,
        enableBackupEncryption: true,
        backupRetention: 30,
        dataRetention: 365
    },
    monitoring: {
        enableActivityLogging: true,
        logRetentionDays: 90,
        alertOnSuspiciousActivity: true,
        notifyAdminOnFailedLogin: true
    }
};

/**
 * Initialize security settings page
 */
function initializeSecuritySettings() {
    console.log('🔒 Initializing security settings...');
    
    // Load settings
    loadSecuritySettings();
    
    // Add specific event listeners
    addSecuritySettingsListeners();
    
    // Initialize security level indicator
    initializeSecurityLevelIndicator();
    
    console.log('✅ Security settings initialized');
}

/**
 * Load security settings
 */
async function loadSecuritySettings() {
    try {
        const response = await fetch('../api/security-settings.php');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success && data.settings) {
            securitySettings = { ...securitySettings, ...data.settings };
            applySettingsToUI();
            updateSecurityLevel();
            console.log('📥 Security settings loaded successfully');
        } else {
            throw new Error(data.message || 'Failed to load security settings');
        }
    } catch (error) {
        console.error('❌ Error loading security settings:', error);
        showNotification('خطأ في تحميل إعدادات الأمان: ' + error.message, 'error');
    }
}

/**
 * Apply settings to UI elements
 */
function applySettingsToUI() {
    // Authentication settings
    document.getElementById('requireStrongPassword').checked = securitySettings.authentication.requireStrongPassword;
    document.getElementById('enableTwoFactor').checked = securitySettings.authentication.enableTwoFactor;
    document.getElementById('sessionTimeout').value = securitySettings.authentication.sessionTimeout;
    document.getElementById('maxLoginAttempts').value = securitySettings.authentication.maxLoginAttempts;
    
    // Access control settings
    document.getElementById('enableIPWhitelist').checked = securitySettings.accessControl.enableIPWhitelist;
    document.getElementById('ipWhitelist').value = securitySettings.accessControl.ipWhitelist.join('\n');
    document.getElementById('enableGeoBlocking').checked = securitySettings.accessControl.enableGeoBlocking;
    
    // Data protection settings
    document.getElementById('enableDataEncryption').checked = securitySettings.dataProtection.enableDataEncryption;
    document.getElementById('enableBackupEncryption').checked = securitySettings.dataProtection.enableBackupEncryption;
    document.getElementById('backupRetention').value = securitySettings.dataProtection.backupRetention;
    document.getElementById('dataRetention').value = securitySettings.dataProtection.dataRetention;
    
    // Show/hide dependent fields
    toggleIPWhitelistField();
}

/**
 * Add security settings specific event listeners
 */
function addSecuritySettingsListeners() {
    // Save button click handler
    const saveBtn = document.querySelector('.enhanced-save-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', saveSecuritySettings);
    }
    
    // IP Whitelist toggle
    const ipWhitelistToggle = document.getElementById('enableIPWhitelist');
    if (ipWhitelistToggle) {
        ipWhitelistToggle.addEventListener('change', toggleIPWhitelistField);
    }
    
    // Form change tracking
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', () => {
            settingsState.isDirty = true;
            updateSaveButtonState();
            if (element.type === 'checkbox' && element.id === 'enableIPWhitelist') {
                toggleIPWhitelistField();
            }
        });
    });
}

/**
 * Toggle IP Whitelist field visibility
 */
function toggleIPWhitelistField() {
    const ipWhitelistGroup = document.getElementById('ipWhitelistGroup');
    const enableIPWhitelist = document.getElementById('enableIPWhitelist');
    
    if (ipWhitelistGroup && enableIPWhitelist) {
        ipWhitelistGroup.style.display = enableIPWhitelist.checked ? 'block' : 'none';
    }
}

/**
 * Save security settings
 */
async function saveSecuritySettings() {
    try {
        // Show loading state
        const saveBtn = document.querySelector('.enhanced-save-btn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        saveBtn.disabled = true;
        
        // Collect and validate settings
        const newSettings = collectSettingsFromUI();
        if (!validateSecuritySettings(newSettings)) {
            throw new Error('يرجى التحقق من صحة جميع الإعدادات');
        }
        
        // Save to API
        await saveToAPI('../api/security-settings.php', newSettings);
        
        // Update local state
        securitySettings = { ...securitySettings, ...newSettings };
        
        // Update security level
        updateSecurityLevel();
        
        // Restore button
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        
    } catch (error) {
        console.error('❌ Error saving security settings:', error);
        showNotification(error.message, 'error');
    }
}

/**
 * Collect settings from UI
 */
function collectSettingsFromUI() {
    return {
        authentication: {
            requireStrongPassword: document.getElementById('requireStrongPassword').checked,
            enableTwoFactor: document.getElementById('enableTwoFactor').checked,
            sessionTimeout: parseInt(document.getElementById('sessionTimeout').value) || 30,
            maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value) || 5
        },
        accessControl: {
            enableIPWhitelist: document.getElementById('enableIPWhitelist').checked,
            ipWhitelist: document.getElementById('ipWhitelist').value.split('\n').filter(ip => ip.trim()),
            enableGeoBlocking: document.getElementById('enableGeoBlocking').checked
        },
        dataProtection: {
            enableDataEncryption: document.getElementById('enableDataEncryption').checked,
            enableBackupEncryption: document.getElementById('enableBackupEncryption').checked,
            backupRetention: parseInt(document.getElementById('backupRetention').value) || 30,
            dataRetention: parseInt(document.getElementById('dataRetention').value) || 365
        }
    };
}

/**
 * Validate security settings
 */
function validateSecuritySettings(settings) {
    // Session timeout validation
    if (settings.authentication.sessionTimeout < 5 || settings.authentication.sessionTimeout > 1440) {
        showNotification('يجب أن تكون مدة الجلسة بين 5 و 1440 دقيقة', 'error');
        return false;
    }
    
    // Login attempts validation
    if (settings.authentication.maxLoginAttempts < 3 || settings.authentication.maxLoginAttempts > 10) {
        showNotification('يجب أن يكون عدد محاولات تسجيل الدخول بين 3 و 10', 'error');
        return false;
    }
    
    // IP Whitelist validation
    if (settings.accessControl.enableIPWhitelist && settings.accessControl.ipWhitelist.length === 0) {
        showNotification('يجب إضافة عنوان IP واحد على الأقل عند تفعيل القائمة البيضاء', 'error');
        return false;
    }
    
    // Validate IP format if whitelist is enabled
    if (settings.accessControl.enableIPWhitelist) {
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const invalidIPs = settings.accessControl.ipWhitelist.filter(ip => !ipRegex.test(ip.trim()));
        
        if (invalidIPs.length > 0) {
            showNotification(`عناوين IP غير صالحة: ${invalidIPs.join(', ')}`, 'error');
            return false;
        }
    }
    
    return true;
}

/**
 * Initialize security level indicator
 */
function initializeSecurityLevelIndicator() {
    const securityLevelElement = document.getElementById('securityLevel');
    if (securityLevelElement) {
        updateSecurityLevel();
    }
}

/**
 * Update security level based on current settings
 */
function updateSecurityLevel() {
    const securityScore = calculateSecurityScore();
    const securityLevelElement = document.getElementById('securityLevel');
    
    if (securityLevelElement) {
        let level, color;
        
        if (securityScore >= 80) {
            level = 'مرتفع';
            color = '#28a745';
        } else if (securityScore >= 60) {
            level = 'متوسط';
            color = '#ffc107';
        } else {
            level = 'منخفض';
            color = '#dc3545';
        }
        
        securityLevelElement.textContent = level;
        securityLevelElement.style.color = color;
    }
}

/**
 * Calculate security score based on enabled settings
 */
function calculateSecurityScore() {
    let score = 0;
    const settings = securitySettings;
    
    // Authentication settings (40 points)
    if (settings.authentication.requireStrongPassword) score += 15;
    if (settings.authentication.enableTwoFactor) score += 15;
    if (settings.authentication.maxLoginAttempts <= 5) score += 5;
    if (settings.authentication.sessionTimeout <= 30) score += 5;
    
    // Access Control (30 points)
    if (settings.accessControl.enableIPWhitelist) score += 15;
    if (settings.accessControl.enableGeoBlocking) score += 15;
    
    // Data Protection (30 points)
    if (settings.dataProtection.enableDataEncryption) score += 15;
    if (settings.dataProtection.enableBackupEncryption) score += 10;
    if (settings.dataProtection.backupRetention <= 30) score += 5;
    
    return score;
}
