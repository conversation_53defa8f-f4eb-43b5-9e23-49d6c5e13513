<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات JSON API</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .result {
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug-slash"></i> اختبار إصلاحات JSON API</h1>
            <p>اختبار شامل لإصلاحات أخطاء JSON وتحسينات معالجة الأخطاء</p>
        </div>

        <!-- Subscriptions API Tests -->
        <div class="test-section">
            <h3>
                <i class="fas fa-crown"></i>
                اختبار API الاشتراكات
                <span class="status-indicator status-info" id="subscriptions-status"></span>
            </h3>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testSubscriptionsGet()">
                    <i class="fas fa-download"></i> جلب الاشتراكات
                </button>
                <button class="btn btn-success" onclick="testSubscriptionsCreate()">
                    <i class="fas fa-plus"></i> إنشاء اشتراك
                </button>
                <button class="btn btn-danger" onclick="testSubscriptionsError()">
                    <i class="fas fa-exclamation-triangle"></i> اختبار خطأ
                </button>
            </div>
            <div id="subscriptions-result" class="result info">جاهز للاختبار...</div>
        </div>

        <!-- Roles API Tests -->
        <div class="test-section">
            <h3>
                <i class="fas fa-user-shield"></i>
                اختبار API الأدوار
                <span class="status-indicator status-info" id="roles-status"></span>
            </h3>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testRolesGet()">
                    <i class="fas fa-download"></i> جلب الأدوار
                </button>
                <button class="btn btn-success" onclick="testRolesCreate()">
                    <i class="fas fa-plus"></i> إنشاء دور
                </button>
                <button class="btn btn-danger" onclick="testRolesError()">
                    <i class="fas fa-exclamation-triangle"></i> اختبار خطأ
                </button>
            </div>
            <div id="roles-result" class="result info">جاهز للاختبار...</div>
        </div>
    </div>

    <!-- Load API Client -->
    <script src="../js/ApiClient.js"></script>
    
    <script>
        // Test functions
        async function testSubscriptionsGet() {
            updateStatus('subscriptions', 'info');
            showResult('subscriptions', 'info', 'جاري اختبار جلب الاشتراكات...');
            
            try {
                const data = await apiClient.get('../php/api/subscriptions-fixed.php?action=plans');
                updateStatus('subscriptions', 'success');
                showResult('subscriptions', 'success', `✅ نجح الاختبار!\nتم جلب ${data.data?.plans?.length || 0} خطة اشتراك\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('subscriptions', 'error');
                showResult('subscriptions', 'error', `❌ فشل الاختبار!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        async function testSubscriptionsCreate() {
            updateStatus('subscriptions', 'info');
            showResult('subscriptions', 'info', 'جاري اختبار إنشاء اشتراك...');
            
            const testPlan = {
                name: 'test_plan_' + Date.now(),
                display_name_ar: 'خطة اختبار',
                display_name_en: 'Test Plan',
                price: 99.99,
                duration_days: 30,
                max_products: 10,
                max_landing_pages: 5
            };
            
            try {
                const data = await apiClient.post('../php/api/subscriptions-fixed.php?action=create_plan', testPlan);
                updateStatus('subscriptions', 'success');
                showResult('subscriptions', 'success', `✅ نجح إنشاء الاشتراك!\nمعرف الخطة: ${data.data?.plan_id}\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('subscriptions', 'error');
                showResult('subscriptions', 'error', `❌ فشل إنشاء الاشتراك!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        async function testSubscriptionsError() {
            updateStatus('subscriptions', 'info');
            showResult('subscriptions', 'info', 'جاري اختبار معالجة الأخطاء...');
            
            try {
                const data = await apiClient.get('../php/api/subscriptions-fixed.php?action=invalid_action');
                updateStatus('subscriptions', 'error');
                showResult('subscriptions', 'error', `❌ كان يجب أن يفشل الاختبار!\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('subscriptions', 'success');
                showResult('subscriptions', 'success', `✅ تم التعامل مع الخطأ بشكل صحيح!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        async function testRolesGet() {
            updateStatus('roles', 'info');
            showResult('roles', 'info', 'جاري اختبار جلب الأدوار...');
            
            try {
                const data = await apiClient.get('../php/api/roles-fixed.php?action=list');
                updateStatus('roles', 'success');
                showResult('roles', 'success', `✅ نجح الاختبار!\nتم جلب ${data.data?.roles?.length || 0} دور\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('roles', 'error');
                showResult('roles', 'error', `❌ فشل الاختبار!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        async function testRolesCreate() {
            updateStatus('roles', 'info');
            showResult('roles', 'info', 'جاري اختبار إنشاء دور...');
            
            const testRole = {
                name: 'test_role_' + Date.now(),
                display_name_ar: 'دور اختبار',
                display_name_en: 'Test Role',
                description: 'دور للاختبار',
                level: 5,
                permissions: ['users.view', 'products.view']
            };
            
            try {
                const data = await apiClient.post('../php/api/roles-fixed.php?action=create', testRole);
                updateStatus('roles', 'success');
                showResult('roles', 'success', `✅ نجح إنشاء الدور!\nمعرف الدور: ${data.data?.role_id}\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('roles', 'error');
                showResult('roles', 'error', `❌ فشل إنشاء الدور!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        async function testRolesError() {
            updateStatus('roles', 'info');
            showResult('roles', 'info', 'جاري اختبار معالجة الأخطاء...');
            
            try {
                const data = await apiClient.get('../php/api/roles-fixed.php?action=invalid_action');
                updateStatus('roles', 'error');
                showResult('roles', 'error', `❌ كان يجب أن يفشل الاختبار!\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                updateStatus('roles', 'success');
                showResult('roles', 'success', `✅ تم التعامل مع الخطأ بشكل صحيح!\nالخطأ: ${error.message}\nالكود: ${error.code}\nالحالة: ${error.status}`);
            }
        }

        // Helper functions
        function updateStatus(section, status) {
            const indicator = document.getElementById(`${section}-status`);
            indicator.className = `status-indicator status-${status}`;
        }

        function showResult(section, type, message) {
            const result = document.getElementById(`${section}-result`);
            result.className = `result ${type}`;
            result.textContent = message;
        }

        // Initialize
        console.log('🧪 JSON API Test Suite loaded successfully');
    </script>
</body>
</html>
