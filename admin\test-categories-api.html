<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API الفئات</h1>
        
        <div>
            <button class="btn-primary" onclick="testGetAll()">جلب جميع الفئات</button>
            <button class="btn-success" onclick="testGetById()">جلب فئة بالمعرف</button>
            <button class="btn-warning" onclick="testCreate()">إنشاء فئة جديدة</button>
            <button class="btn-danger" onclick="testUpdate()">تحديث فئة</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        async function testGetAll() {
            try {
                console.log('🔄 اختبار جلب جميع الفئات...');
                const response = await fetch('php/categories.php?action=get_all');
                const data = await response.json();
                console.log('📦 النتيجة:', data);
                showResult(data);
            } catch (error) {
                console.error('❌ خطأ:', error);
                showResult({ error: error.message }, true);
            }
        }

        async function testGetById() {
            const id = prompt('أدخل معرف الفئة:');
            if (!id) return;
            
            try {
                console.log('🔄 اختبار جلب فئة بالمعرف:', id);
                const response = await fetch(`php/categories.php?action=get_by_id&id=${id}`);
                const data = await response.json();
                console.log('📦 النتيجة:', data);
                showResult(data);
            } catch (error) {
                console.error('❌ خطأ:', error);
                showResult({ error: error.message }, true);
            }
        }

        async function testCreate() {
            const categoryData = {
                name_ar: 'فئة تجريبية',
                name_en: 'Test Category',
                description_ar: 'وصف الفئة التجريبية',
                color: '#667eea',
                icon: 'fas fa-test',
                is_active: 1,
                is_featured: 0,
                sort_order: 0
            };
            
            try {
                console.log('🔄 اختبار إنشاء فئة جديدة:', categoryData);
                const response = await fetch('php/categories.php?action=create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(categoryData)
                });
                const data = await response.json();
                console.log('📦 النتيجة:', data);
                showResult(data);
            } catch (error) {
                console.error('❌ خطأ:', error);
                showResult({ error: error.message }, true);
            }
        }

        async function testUpdate() {
            const id = prompt('أدخل معرف الفئة للتحديث:');
            if (!id) return;
            
            const categoryData = {
                name_ar: 'فئة محدثة',
                name_en: 'Updated Category',
                description_ar: 'وصف الفئة المحدثة',
                color: '#28a745',
                icon: 'fas fa-updated',
                is_active: 1,
                is_featured: 1,
                sort_order: 1
            };
            
            try {
                console.log('🔄 اختبار تحديث فئة:', id, categoryData);
                const response = await fetch(`php/categories.php?action=update&id=${id}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(categoryData)
                });
                const data = await response.json();
                console.log('📦 النتيجة:', data);
                showResult(data);
            } catch (error) {
                console.error('❌ خطأ:', error);
                showResult({ error: error.message }, true);
            }
        }

        // Test on page load
        window.addEventListener('load', () => {
            console.log('🚀 صفحة اختبار API الفئات جاهزة');
            testGetAll();
        });
    </script>
</body>
</html>
