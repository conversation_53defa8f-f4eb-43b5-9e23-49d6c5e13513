<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات الأخطاء</title>
    <link rel="stylesheet" href="css/multi-user-admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-bug"></i> اختبار إصلاحات الأخطاء الحرجة</h1>
        <p>هذه الصفحة تختبر جميع الإصلاحات التي تم تطبيقها على واجهة الإدارة متعددة المستخدمين.</p>

        <!-- Test 1: Product Management Functions -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار 1: وظائف إدارة المنتجات</h3>
            <p>اختبار جميع الوظائف المطلوبة لإدارة المنتجات (editProduct, viewProduct, deleteProduct, viewLandingPage)</p>
            <button class="test-button" onclick="testProductManagementFunctions()">تشغيل الاختبار</button>
            <div id="productManagementTestResult"></div>
        </div>

        <!-- Test 2: Products Pagination -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> اختبار 2: تقسيم صفحات المنتجات</h3>
            <p>اختبار وظيفة addViewMoreLink ونظام التقسيم</p>
            <button class="test-button" onclick="testProductsPagination()">تشغيل الاختبار</button>
            <div id="paginationTestResult"></div>
        </div>

        <!-- Test 3: Reports Section -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> اختبار 3: قسم التقارير</h3>
            <p>اختبار تحميل قسم التقارير والإحصائيات</p>
            <button class="test-button" onclick="testReportsSection()">تشغيل الاختبار</button>
            <div id="reportsTestResult"></div>
        </div>

        <!-- Test 4: Settings Sections -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> اختبار 4: أقسام الإعدادات</h3>
            <p>اختبار جميع أقسام الإعدادات</p>
            <button class="test-button" onclick="testSettingsSections()">تشغيل الاختبار</button>
            <div id="settingsTestResult"></div>
        </div>

        <!-- Test 5: Landing Pages Section -->
        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> اختبار 5: قسم صفحات الهبوط</h3>
            <p>اختبار قسم صفحات الهبوط مع التقسيم ومعلومات المالك</p>
            <button class="test-button" onclick="testLandingPagesSection()">تشغيل الاختبار</button>
            <div id="landingPagesTestResult"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> مخرجات وحدة التحكم</h3>
            <div id="consoleOutput" class="console-output">جاهز لعرض مخرجات الاختبارات...</div>
        </div>

        <!-- Run All Tests -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" style="background: #28a745; font-size: 16px; padding: 15px 30px;" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
        </div>
    </div>

    <!-- Include the JavaScript files to test -->
    <script src="js/admin.js"></script>
    <script src="js/products-pagination.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/admin-sections-fix.js"></script>

    <script>
        // Console capture for testing
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        let consoleOutput = '';

        function captureConsole() {
            console.log = function(...args) {
                consoleOutput += '[LOG] ' + args.join(' ') + '\n';
                originalConsole.log.apply(console, args);
                updateConsoleDisplay();
            };

            console.error = function(...args) {
                consoleOutput += '[ERROR] ' + args.join(' ') + '\n';
                originalConsole.error.apply(console, args);
                updateConsoleDisplay();
            };

            console.warn = function(...args) {
                consoleOutput += '[WARN] ' + args.join(' ') + '\n';
                originalConsole.warn.apply(console, args);
                updateConsoleDisplay();
            };
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('consoleOutput');
            if (consoleDiv) {
                consoleDiv.textContent = consoleOutput || 'لا توجد مخرجات...';
                consoleDiv.scrollTop = consoleDiv.scrollHeight;
            }
        }

        function clearConsole() {
            consoleOutput = '';
            updateConsoleDisplay();
        }

        // Test functions
        function testProductManagementFunctions() {
            const resultDiv = document.getElementById('productManagementTestResult');
            try {
                // Test if all required product management functions exist
                const productFunctions = [
                    { name: 'editProduct', func: window.editProduct, description: 'تعديل المنتج' },
                    { name: 'viewProduct', func: window.viewProduct, description: 'عرض تفاصيل المنتج' },
                    { name: 'deleteProduct', func: window.deleteProduct, description: 'حذف المنتج' },
                    { name: 'viewLandingPage', func: window.viewLandingPage, description: 'عرض صفحة الهبوط' },
                    { name: 'showProductViewModal', func: window.showProductViewModal, description: 'عرض نافذة تفاصيل المنتج' },
                    { name: 'closeProductViewModal', func: window.closeProductViewModal, description: 'إغلاق نافذة تفاصيل المنتج' }
                ];

                let results = '<h4>🔍 اختبار وظائف إدارة المنتجات:</h4>';
                let allPassed = true;

                productFunctions.forEach(test => {
                    if (typeof test.func === 'function') {
                        results += `<div class="test-success">✓ ${test.name} - ${test.description} (موجودة ومعرفة بشكل صحيح)</div>`;
                    } else {
                        results += `<div class="test-error">✗ ${test.name} - ${test.description} (غير موجودة أو غير معرفة)</div>`;
                        allPassed = false;
                    }
                });

                // Test pagination functions compatibility
                const paginationFunctions = [
                    { name: 'changeProductsPerPage', func: window.changeProductsPerPage },
                    { name: 'searchProducts', func: window.searchProducts },
                    { name: 'addViewMoreLink', func: window.addViewMoreLink }
                ];

                results += '<h4>🔄 اختبار توافق وظائف التقسيم:</h4>';
                paginationFunctions.forEach(test => {
                    if (typeof test.func === 'function') {
                        results += `<div class="test-success">✓ ${test.name} متوافقة مع نظام التقسيم</div>`;
                    } else {
                        results += `<div class="test-error">✗ ${test.name} غير متوافقة مع نظام التقسيم</div>`;
                        allPassed = false;
                    }
                });

                // Test sample product data for modal
                results += '<h4>📊 اختبار البيانات التجريبية:</h4>';
                try {
                    const sampleProduct = {
                        id: 1,
                        nom: 'منتج تجريبي',
                        prix: '1500',
                        categorie: 'إلكترونيات',
                        description: 'وصف المنتج التجريبي',
                        actif: true,
                        owner_name: 'أحمد محمد',
                        owner_id: 1,
                        landing_page_url: 'https://example.com/landing'
                    };

                    results += '<div class="test-success">✓ بنية البيانات التجريبية صحيحة</div>';
                    results += `<div class="test-success">✓ البيانات تتضمن: الاسم، السعر، الفئة، الوصف، الحالة، معلومات المالك</div>`;
                } catch (error) {
                    results += `<div class="test-error">✗ خطأ في البيانات التجريبية: ${error.message}</div>`;
                    allPassed = false;
                }

                // Final result
                if (allPassed) {
                    results += '<div class="test-success"><strong>🎉 جميع وظائف إدارة المنتجات جاهزة وتعمل بشكل صحيح!</strong></div>';
                    results += '<div class="test-success">✅ لا توجد أخطاء ReferenceError في وظائف المنتجات</div>';
                    results += '<div class="test-success">✅ التوافق مع نظام التقسيم محفوظ</div>';
                    results += '<div class="test-success">✅ الدعم متعدد المستخدمين مع معلومات المالك</div>';

                    // Add test buttons for actual functionality
                    results += '<div style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-radius: 5px;">';
                    results += '<h5>🧪 اختبار الوظائف الفعلية:</h5>';
                    results += '<button class="test-button" onclick="testViewProductModal()" style="margin: 5px;">اختبار عرض المنتج</button>';
                    results += '<button class="test-button" onclick="testEditProductFunction()" style="margin: 5px;">اختبار تعديل المنتج</button>';
                    results += '<button class="test-button" onclick="testDeleteProductFunction()" style="margin: 5px;">اختبار حذف المنتج</button>';
                    results += '</div>';
                } else {
                    results += '<div class="test-error"><strong>❌ بعض وظائف إدارة المنتجات مفقودة أو لا تعمل بشكل صحيح</strong></div>';
                    results += '<div class="test-error">⚠️ قد تحدث أخطاء ReferenceError عند استخدام أزرار المنتجات</div>';
                }

                resultDiv.innerHTML = results;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error">✗ خطأ في اختبار وظائف إدارة المنتجات: ${error.message}</div>`;
            }
        }

        // Test actual product view modal
        function testViewProductModal() {
            const sampleProduct = {
                id: 999,
                nom: 'منتج تجريبي للاختبار',
                prix: '2500',
                categorie: 'إلكترونيات',
                description: 'هذا منتج تجريبي لاختبار وظيفة عرض تفاصيل المنتج.\n\nيتضمن جميع الحقول المطلوبة ومعلومات المالك.',
                actif: true,
                owner_name: 'أحمد محمد (مستخدم تجريبي)',
                owner_id: 1,
                landing_page_url: 'https://example.com/landing-page-test',
                image: null
            };

            if (typeof window.showProductViewModal === 'function') {
                window.showProductViewModal(sampleProduct);
                console.log('✅ تم اختبار نافذة عرض المنتج بنجاح');
            } else {
                alert('❌ وظيفة showProductViewModal غير موجودة');
            }
        }

        // Test edit product function
        function testEditProductFunction() {
            if (typeof window.editProduct === 'function') {
                console.log('🔧 اختبار وظيفة تعديل المنتج...');
                alert('✅ وظيفة editProduct موجودة ومعرفة بشكل صحيح\n\nملاحظة: هذا اختبار للوظيفة فقط، لم يتم تنفيذ التعديل الفعلي.');
            } else {
                alert('❌ وظيفة editProduct غير موجودة');
            }
        }

        // Test delete product function
        function testDeleteProductFunction() {
            if (typeof window.deleteProduct === 'function') {
                console.log('🗑️ اختبار وظيفة حذف المنتج...');
                alert('✅ وظيفة deleteProduct موجودة ومعرفة بشكل صحيح\n\nملاحظة: هذا اختبار للوظيفة فقط، لم يتم تنفيذ الحذف الفعلي.');
            } else {
                alert('❌ وظيفة deleteProduct غير موجودة');
            }
        }

        function testProductsPagination() {
            const resultDiv = document.getElementById('paginationTestResult');
            try {
                let results = '';
                let allPassed = true;

                // Test critical pagination functions
                const paginationTests = [
                    { name: 'addViewMoreLink', func: window.addViewMoreLink },
                    { name: 'changeProductsPerPage', func: window.changeProductsPerPage },
                    { name: 'changeProductsPageSize', func: window.changeProductsPageSize },
                    { name: 'searchProducts', func: window.searchProducts },
                    { name: 'goToProductsPage', func: window.goToProductsPage },
                    { name: 'previousProductsPage', func: window.previousProductsPage },
                    { name: 'nextProductsPage', func: window.nextProductsPage }
                ];

                paginationTests.forEach(test => {
                    if (typeof test.func === 'function') {
                        results += `<div class="test-success">✓ ${test.name} موجودة ومعرفة بشكل صحيح</div>`;
                    } else {
                        results += `<div class="test-error">✗ ${test.name} غير موجودة أو غير معرفة</div>`;
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    results += '<div class="test-success"><strong>✓ جميع وظائف تقسيم المنتجات تعمل بشكل صحيح</strong></div>';
                } else {
                    results += '<div class="test-error"><strong>✗ بعض وظائف التقسيم مفقودة</strong></div>';
                }

                resultDiv.innerHTML = results;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error">✗ خطأ في اختبار التقسيم: ${error.message}</div>`;
            }
        }

        function testReportsSection() {
            const resultDiv = document.getElementById('reportsTestResult');
            try {
                let results = '';
                let allPassed = true;

                // Test reports functions
                const reportsTests = [
                    { name: 'loadReportsContent (admin.js)', func: window.loadReportsContent },
                    { name: 'initializeReports (reports.js)', func: window.initializeReports },
                    { name: 'renderReportsContent (reports.js)', func: window.renderReportsContent }
                ];

                reportsTests.forEach(test => {
                    if (typeof test.func === 'function') {
                        results += `<div class="test-success">✓ ${test.name} موجودة ومعرفة بشكل صحيح</div>`;
                    } else {
                        results += `<div class="test-error">✗ ${test.name} غير موجودة أو غير معرفة</div>`;
                        allPassed = false;
                    }
                });

                // Test if reports container exists
                const reportsContainer = document.getElementById('reportsContent');
                if (reportsContainer) {
                    results += '<div class="test-success">✓ حاوية التقارير موجودة في HTML</div>';
                } else {
                    results += '<div class="test-error">✗ حاوية التقارير غير موجودة في HTML</div>';
                    allPassed = false;
                }

                if (allPassed) {
                    results += '<div class="test-success"><strong>✓ قسم التقارير جاهز للعمل بشكل صحيح</strong></div>';

                    // Try to actually load reports
                    results += '<div class="test-warning">🔄 اختبار تحميل التقارير الفعلي...</div>';
                    setTimeout(() => {
                        if (typeof window.loadReportsContent === 'function') {
                            window.loadReportsContent();
                        }
                    }, 1000);
                } else {
                    results += '<div class="test-error"><strong>✗ قسم التقارير يحتاج إصلاحات</strong></div>';
                }

                resultDiv.innerHTML = results;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error">✗ خطأ في اختبار التقارير: ${error.message}</div>`;
            }
        }

        function testSettingsSections() {
            const resultDiv = document.getElementById('settingsTestResult');
            try {
                const settingsFunctions = [
                    'loadGeneralSettingsContent',
                    'loadStoreSettingsContent',
                    'loadUserManagementContent',
                    'loadStoresManagementContent',
                    'loadRolesManagementContent',
                    'loadSubscriptionsManagementContent',
                    'loadSecuritySettingsContent',
                    'loadSystemTestingContent'
                ];

                let results = '';
                let passedCount = 0;

                settingsFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        results += `<div class="test-success">✓ ${funcName}</div>`;
                        passedCount++;
                    } else {
                        results += `<div class="test-error">✗ ${funcName}</div>`;
                    }
                });

                results += `<div class="test-${passedCount === settingsFunctions.length ? 'success' : 'warning'}">
                    <strong>${passedCount}/${settingsFunctions.length} وظائف الإعدادات موجودة</strong>
                </div>`;

                resultDiv.innerHTML = results;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error">✗ خطأ في اختبار الإعدادات: ${error.message}</div>`;
            }
        }

        function testLandingPagesSection() {
            const resultDiv = document.getElementById('landingPagesTestResult');
            try {
                let results = '';
                let allPassed = true;

                // Test landing pages functions
                const landingPagesTests = [
                    { name: 'loadLandingPages', func: window.loadLandingPages },
                    { name: 'initializeLandingPagesData', func: window.initializeLandingPagesData },
                    { name: 'renderLandingPagesPage', func: window.renderLandingPagesPage },
                    { name: 'changeLandingPagesPerPage', func: window.changeLandingPagesPerPage },
                    { name: 'searchLandingPages', func: window.searchLandingPages }
                ];

                landingPagesTests.forEach(test => {
                    if (typeof test.func === 'function') {
                        results += `<div class="test-success">✓ ${test.name} موجودة ومعرفة بشكل صحيح</div>`;
                    } else {
                        results += `<div class="test-error">✗ ${test.name} غير موجودة أو غير معرفة</div>`;
                        allPassed = false;
                    }
                });

                // Test if landing pages container exists
                const landingPagesContainer = document.getElementById('landingPagesContainer');
                if (landingPagesContainer) {
                    results += '<div class="test-success">✓ حاوية صفحات الهبوط موجودة في HTML</div>';
                } else {
                    results += '<div class="test-error">✗ حاوية صفحات الهبوط غير موجودة في HTML</div>';
                    allPassed = false;
                }

                // Test sample data structure for owner information
                if (typeof window.getSampleLandingPagesData === 'function') {
                    const sampleData = window.getSampleLandingPagesData();
                    if (sampleData && sampleData.length > 0) {
                        const firstPage = sampleData[0];
                        if (firstPage.owner_name && firstPage.owner_id) {
                            results += '<div class="test-success">✓ بيانات المالك متوفرة (owner_name, owner_id)</div>';
                        } else {
                            results += '<div class="test-error">✗ بيانات المالك مفقودة في البيانات التجريبية</div>';
                            allPassed = false;
                        }
                    }
                }

                if (allPassed) {
                    results += '<div class="test-success"><strong>✓ قسم صفحات الهبوط مع معلومات المالك جاهز</strong></div>';
                } else {
                    results += '<div class="test-error"><strong>✗ قسم صفحات الهبوط يحتاج إصلاحات</strong></div>';
                }

                resultDiv.innerHTML = results;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error">✗ خطأ في اختبار صفحات الهبوط: ${error.message}</div>`;
            }
        }

        function runAllTests() {
            clearConsole();
            console.log('🚀 بدء تشغيل جميع الاختبارات الحرجة...');

            testProductManagementFunctions();
            testProductsPagination();
            testReportsSection();
            testSettingsSections();
            testLandingPagesSection();

            console.log('✅ انتهاء جميع الاختبارات الحرجة.');
            console.log('📋 تحقق من النتائج أعلاه للتأكد من عدم وجود أخطاء ReferenceError.');
        }

        // Initialize console capture
        captureConsole();

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
