<?php
/**
 * Final Comprehensive Test
 * الاختبار الشامل النهائي
 */

require_once 'php/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار الشامل النهائي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        h1, h2, h3 { color: #333; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: white; border: 1px solid #ddd; border-radius: 10px; padding: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-button:hover { background: #0056b3; }
        .status-indicator { display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-left: 10px; }
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 الاختبار الشامل النهائي</h1>
        <p>فحص شامل لجميع الإصلاحات والمكونات</p>

        <?php
        $overallStatus = 'success';
        $testResults = [];
        
        try {
            $pdo = getPDOConnection();
            
            // Test 1: Database Connection
            echo "<div class='section success'>";
            echo "<h2>✅ 1. اختبار الاتصال بقاعدة البيانات</h2>";
            echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
            $testResults['database_connection'] = 'PASS';
            echo "</div>";
            
            // Test 2: Database Migration Status
            echo "<div class='section'>";
            echo "<h2>🔧 2. حالة ترحيل قاعدة البيانات</h2>";
            
            // Check produits table
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
            $produitsCount = $stmt->fetch()['count'];
            echo "<div class='success'>✅ جدول produits: $produitsCount منتج</div>";
            $testResults['produits_table'] = 'PASS';
            
            // Check foreign keys
            $stmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME = 'produits'
            ");
            $fkCount = $stmt->fetch()['count'];
            echo "<div class='success'>✅ المفاتيح الخارجية: $fkCount مفتاح يشير إلى produits</div>";
            $testResults['foreign_keys'] = 'PASS';
            echo "</div>";
            
            // Test 3: Role Management System
            echo "<div class='section'>";
            echo "<h2>🛡️ 3. نظام إدارة الأدوار</h2>";
            
            // Check user_roles table
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
            $rolesCount = $stmt->fetch()['count'];
            echo "<div class='success'>✅ جدول user_roles: $rolesCount دور</div>";
            $testResults['user_roles_table'] = 'PASS';
            
            // Check users table
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $usersCount = $stmt->fetch()['count'];
            echo "<div class='success'>✅ جدول users: $usersCount مستخدم</div>";
            $testResults['users_table'] = 'PASS';
            
            // Test roles API
            $rolesApiUrl = 'http://localhost:8080/php/api/roles.php?action=list';
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'method' => 'GET'
                ]
            ]);
            
            $apiResponse = @file_get_contents($rolesApiUrl, false, $context);
            if ($apiResponse) {
                $apiData = json_decode($apiResponse, true);
                if ($apiData && isset($apiData['success']) && $apiData['success']) {
                    echo "<div class='success'>✅ API إدارة الأدوار يعمل بشكل صحيح</div>";
                    $testResults['roles_api'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ API إدارة الأدوار يعيد خطأ</div>";
                    $testResults['roles_api'] = 'FAIL';
                    $overallStatus = 'error';
                }
            } else {
                echo "<div class='warning'>⚠️ لا يمكن الوصول إلى API إدارة الأدوار</div>";
                $testResults['roles_api'] = 'WARNING';
            }
            echo "</div>";
            
            // Test 4: File System
            echo "<div class='section'>";
            echo "<h2>📁 4. نظام الملفات</h2>";
            
            $criticalFiles = [
                'database/run_safe_migration.php' => 'أداة الترحيل الآمنة',
                'admin/js/users-management.js' => 'JavaScript إدارة المستخدمين',
                'php/api/roles.php' => 'API إدارة الأدوار',
                'admin/index.html' => 'لوحة التحكم الرئيسية'
            ];
            
            foreach ($criticalFiles as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='success'>✅ $description موجود</div>";
                    $testResults["file_$file"] = 'PASS';
                } else {
                    echo "<div class='error'>❌ $description غير موجود</div>";
                    $testResults["file_$file"] = 'FAIL';
                    $overallStatus = 'error';
                }
            }
            echo "</div>";
            
            // Test 5: JavaScript Functions
            echo "<div class='section'>";
            echo "<h2>⚙️ 5. وظائف JavaScript</h2>";
            
            if (file_exists('admin/js/users-management.js')) {
                $jsContent = file_get_contents('admin/js/users-management.js');
                
                $jsFunction = [
                    'loadRolesManagementContent' => 'تحميل محتوى إدارة الأدوار',
                    'showRolesManagement' => 'عرض إدارة الأدوار',
                    'loadRolesManagementInterface' => 'تحميل واجهة إدارة الأدوار',
                    'saveRole' => 'حفظ الدور',
                    'editRole' => 'تعديل الدور',
                    'deleteRole' => 'حذف الدور'
                ];
                
                $functionsFound = 0;
                foreach ($jsFunction as $func => $desc) {
                    if (strpos($jsContent, "function $func") !== false) {
                        echo "<div class='success'>✅ $desc ($func)</div>";
                        $functionsFound++;
                        $testResults["js_function_$func"] = 'PASS';
                    } else {
                        echo "<div class='error'>❌ $desc ($func) مفقودة</div>";
                        $testResults["js_function_$func"] = 'FAIL';
                        $overallStatus = 'error';
                    }
                }
                
                echo "<div class='info'>📊 الوظائف الموجودة: $functionsFound/" . count($jsFunction) . "</div>";
            }
            echo "</div>";
            
            // Test Summary
            echo "<div class='section'>";
            echo "<h2>📊 6. ملخص النتائج</h2>";
            
            $totalTests = count($testResults);
            $passedTests = count(array_filter($testResults, function($result) { return $result === 'PASS'; }));
            $failedTests = count(array_filter($testResults, function($result) { return $result === 'FAIL'; }));
            $warningTests = count(array_filter($testResults, function($result) { return $result === 'WARNING'; }));
            
            echo "<div class='test-grid'>";
            
            echo "<div class='test-card' style='background: #d4edda; border-color: #c3e6cb;'>";
            echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ نجح</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #155724;'>$passedTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #f8d7da; border-color: #f5c6cb;'>";
            echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ فشل</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #721c24;'>$failedTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #fff3cd; border-color: #ffeaa7;'>";
            echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠️ تحذير</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #856404;'>$warningTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #d1ecf1; border-color: #bee5eb;'>";
            echo "<h3 style='color: #0c5460; margin: 0 0 10px 0;'>📊 المجموع</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #0c5460;'>$totalTests</p>";
            echo "</div>";
            
            echo "</div>";
            
            $successRate = round(($passedTests / $totalTests) * 100, 1);
            
            if ($successRate >= 95) {
                echo "<div class='success'>";
                echo "<h3>🎉 ممتاز! معدل النجاح: $successRate%</h3>";
                echo "<p>النظام يعمل بشكل مثالي وجميع المكونات الحرجة تعمل بنجاح</p>";
                echo "<p><strong>✅ جاهز للإنتاج!</strong></p>";
            } elseif ($successRate >= 85) {
                echo "<div class='warning'>";
                echo "<h3>⚠️ جيد جداً - معدل النجاح: $successRate%</h3>";
                echo "<p>النظام يعمل بشكل جيد مع بعض المشاكل البسيطة</p>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ يحتاج إلى تحسين - معدل النجاح: $successRate%</h3>";
                echo "<p>هناك مشاكل تحتاج إلى إصلاح</p>";
            }
            echo "</div>";
            echo "</div>";
            
            // Quick Action Links
            echo "<div class='section info'>";
            echo "<h3>🔗 روابط سريعة للاختبار</h3>";
            echo "<a href='database/run_safe_migration.php' class='test-button'>🔧 أداة الترحيل الآمنة</a>";
            echo "<a href='admin/index.html' class='test-button'>🏠 لوحة التحكم</a>";
            echo "<a href='debug-role-management.html' class='test-button'>🛡️ تشخيص إدارة الأدوار</a>";
            echo "<a href='test-role-api.php' class='test-button'>🔗 اختبار API الأدوار</a>";
            echo "<a href='quick-db-test.php' class='test-button'>📊 اختبار قاعدة البيانات</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h2>❌ خطأ في الاختبار</h2>";
            echo "<p>حدث خطأ أثناء تشغيل الاختبارات: " . $e->getMessage() . "</p>";
            echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
