/**
 * Admin Navigation Fix - Complete Navigation System
 * إصلاح نظام التنقل الكامل للوحة التحكم
 */

(function() {
    "use strict";

    console.log("🔧 Admin Navigation Fix loading...");

    // Navigation state management
    let currentSection = 'dashboard';
    let navigationInitialized = false;

    // Initialize navigation system
    function initializeNavigation() {
        if (navigationInitialized) {
            console.log("Navigation already initialized");
            return;
        }

        console.log("🚀 Initializing navigation system...");

        // Ensure only dashboard is visible initially
        hideAllSections();
        showSection('dashboard');
        setActiveNavItem('dashboard');

        // Setup navigation event listeners
        setupNavigationListeners();

        navigationInitialized = true;
        console.log("✅ Navigation system initialized");
    }

    // Hide all content sections
    function hideAllSections() {
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
            section.style.setProperty('display', 'none', 'important');
            section.style.setProperty('opacity', '0', 'important');
            section.style.setProperty('visibility', 'hidden', 'important');
            section.style.setProperty('position', 'absolute', 'important');
            section.style.setProperty('left', '-9999px', 'important');
        });
    }

    // Show specific section
    function showSection(sectionId) {
        console.log(`📄 Showing section: ${sectionId}`);

        const section = document.getElementById(sectionId);
        if (section) {
            section.classList.add('active');
            section.style.setProperty('display', 'block', 'important');
            section.style.setProperty('opacity', '1', 'important');
            section.style.setProperty('visibility', 'visible', 'important');
            section.style.setProperty('position', 'static', 'important');
            section.style.setProperty('left', 'auto', 'important');

            // Load section-specific content
            loadSectionContent(sectionId);

            currentSection = sectionId;
            console.log(`✅ Section ${sectionId} is now active`);
        } else {
            console.error(`❌ Section ${sectionId} not found`);
        }
    }

    // Set active navigation item
    function setActiveNavItem(sectionId) {
        // Remove active class from all nav items
        const navItems = document.querySelectorAll('.admin-nav ul li');
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current nav item
        const activeNavItem = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }

    // Setup navigation event listeners
    function setupNavigationListeners() {
        console.log("🔗 Setting up navigation listeners...");

        // Main navigation items
        const navItems = document.querySelectorAll('.admin-nav ul li[data-section]');
        console.log(`Found ${navItems.length} navigation items`);

        navItems.forEach((item, index) => {
            // Remove existing listeners to prevent duplicates
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);

            newItem.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const sectionId = this.getAttribute('data-section');
                console.log(`🖱️ Navigation clicked: ${sectionId}`);

                if (sectionId && sectionId !== currentSection) {
                    navigateToSection(sectionId);
                }
            });

            console.log(`✅ Listener added to: ${newItem.getAttribute('data-section')}`);
        });

        // Settings sub-navigation
        const settingCards = document.querySelectorAll('.setting-card[data-section]');
        settingCards.forEach(card => {
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);

            newCard.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const sectionId = this.getAttribute('data-section');
                console.log(`🖱️ Settings card clicked: ${sectionId}`);

                navigateToSection(sectionId);
            });
        });

        console.log("✅ Navigation listeners setup complete");
    }

    // Navigate to section
    function navigateToSection(sectionId) {
        console.log(`🧭 Navigating to section: ${sectionId}`);

        // Hide all sections
        hideAllSections();

        // Show target section
        showSection(sectionId);

        // Update navigation
        setActiveNavItem(sectionId);

        console.log(`✅ Navigation to ${sectionId} complete`);
    }

    // Load section-specific content
    function loadSectionContent(sectionId) {
        console.log(`📦 Loading content for section: ${sectionId}`);

        switch(sectionId) {
            case 'dashboard':
                if (typeof loadDashboard === 'function') {
                    loadDashboard();
                }
                break;
            case 'books':
                if (typeof loadProducts === 'function') {
                    loadProducts();
                }
                break;
            case 'orders':
                if (typeof loadOrders === 'function') {
                    loadOrders();
                }
                break;
            case 'landingPages':
                if (typeof landingPagesManager !== 'undefined' && landingPagesManager.init) {
                    landingPagesManager.init();
                }
                break;
            case 'reports':
                if (typeof loadReportsContent === 'function') {
                    loadReportsContent();
                }
                break;
            case 'settings':
                // Settings section is static, no loading needed
                break;
            default:
                // For sub-sections like categoriesManagement, etc.
                loadSubSectionContent(sectionId);
                break;
        }
    }

    // Load sub-section content (for settings sub-sections)
    function loadSubSectionContent(sectionId) {
        console.log(`📋 Loading sub-section: ${sectionId}`);

        // First, make sure settings nav item is active
        setActiveNavItem('settings');

        // Load specific sub-section content
        switch(sectionId) {
            case 'categoriesManagement':
                if (typeof loadCategoriesManagementContent === 'function') {
                    loadCategoriesManagementContent();
                }
                break;
            case 'paymentSettings':
                // Load payment settings content
                if (typeof loadPaymentSettingsContent === 'function') {
                    loadPaymentSettingsContent();
                } else {
                    // Load the script if not already loaded
                    const script = document.createElement('script');
                    script.src = 'js/load-payment-settings.js';
                    script.onload = () => {
                        if (typeof loadPaymentSettingsContent === 'function') {
                            loadPaymentSettingsContent();
                        }
                    };
                    document.head.appendChild(script);
                }
                break;
            case 'generalSettings':
                if (typeof loadGeneralSettingsContent === 'function') {
                    loadGeneralSettingsContent();
                }
                break;
            case 'storeSettings':
                if (typeof loadStoreSettingsContent === 'function') {
                    loadStoreSettingsContent();
                }
                break;
            case 'userManagement':
                if (typeof loadUserManagementContent === 'function') {
                    loadUserManagementContent();
                }
                break;
            case 'storesManagement':
                if (typeof loadStoresManagementContent === 'function') {
                    loadStoresManagementContent();
                }
                break;
            case 'rolesManagement':
                if (typeof loadRolesManagementContent === 'function') {
                    loadRolesManagementContent();
                }
                break;
            case 'subscriptionsManagement':
                if (typeof loadSubscriptionsManagementContent === 'function') {
                    loadSubscriptionsManagementContent();
                }
                break;
            case 'securitySettings':
                if (typeof loadSecuritySettingsContent === 'function') {
                    loadSecuritySettingsContent();
                }
                break;
            case 'systemTesting':
                if (typeof loadSystemTestingContent === 'function') {
                    loadSystemTestingContent();
                }
                break;
            default:
                console.warn(`Unknown sub-section: ${sectionId}`);
        }
    }

    // Force clean state on page load
    function forceCleanState() {
        console.log("🧹 Forcing clean navigation state...");

        // Hide all sections immediately with !important styles
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            if (section.id !== 'dashboard') {
                section.classList.remove('active');
                section.style.setProperty('display', 'none', 'important');
                section.style.setProperty('opacity', '0', 'important');
                section.style.setProperty('visibility', 'hidden', 'important');
                section.style.setProperty('position', 'absolute', 'important');
                section.style.setProperty('left', '-9999px', 'important');
            }
        });

        // Ensure dashboard is visible
        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            dashboard.classList.add('active');
            dashboard.style.setProperty('display', 'block', 'important');
            dashboard.style.setProperty('opacity', '1', 'important');
            dashboard.style.setProperty('visibility', 'visible', 'important');
            dashboard.style.setProperty('position', 'static', 'important');
            dashboard.style.setProperty('left', 'auto', 'important');
        }

        // Set dashboard nav as active
        setActiveNavItem('dashboard');

        console.log("✅ Clean state enforced with !important styles");
    }

    // Public API
    window.adminNavigation = {
        navigateToSection: navigateToSection,
        showSection: showSection,
        hideAllSections: hideAllSections,
        getCurrentSection: () => currentSection,
        forceCleanState: forceCleanState,
        reinitialize: () => {
            navigationInitialized = false;
            initializeNavigation();
        }
    };

    // Make showSection globally available for onclick handlers
    window.showSection = showSection;

    // Initialize everything when DOM is ready
    function initializeWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    forceCleanState();
                    initializeNavigation();
                }, 100);
            });
        } else {
            setTimeout(() => {
                forceCleanState();
                initializeNavigation();
            }, 100);
        }

        // Also run after a delay to ensure all scripts are loaded
        setTimeout(() => {
            if (!navigationInitialized) {
                console.log("🔄 Reinitializing navigation...");
                forceCleanState();
                initializeNavigation();
            }
        }, 2000);
    }

    // Start initialization
    initializeWhenReady();

    console.log("✅ Admin Navigation Fix loaded successfully");

})();
