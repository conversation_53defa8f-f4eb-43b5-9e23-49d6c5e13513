<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جميع الإصلاحات - إدارة المنتجات</title>
    <link rel="stylesheet" href="css/multi-user-admin.css">
    <link rel="stylesheet" href="css/header-space-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-card h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-pass {
            background: #28a745;
        }
        .status-fail {
            background: #dc3545;
        }
        .status-warning {
            background: #ffc107;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            direction: ltr;
        }
        .demo-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        .demo-modal.show {
            display: flex;
        }
        .demo-modal-content {
            background: white;
            border-radius: 10px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            direction: rtl;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-tools"></i> اختبار شامل لجميع إصلاحات إدارة المنتجات</h1>
        <p>هذه الصفحة تختبر جميع الإصلاحات المطبقة على نظام إدارة المنتجات متعدد المستخدمين.</p>

        <!-- Header Space Test -->
        <div class="test-section">
            <h3><i class="fas fa-arrows-alt-v"></i> اختبار مساحة الرأس</h3>
            <p>فحص ما إذا كانت هناك مساحة فارغة في أعلى الواجهة</p>
            <button class="test-button" onclick="testHeaderSpace()">فحص مساحة الرأس</button>
            <div id="headerSpaceResult"></div>
        </div>

        <!-- Product Management Functions Test -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار وظائف إدارة المنتجات</h3>
            <p>فحص توفر وعمل جميع وظائف إدارة المنتجات</p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-eye"></i> عرض المنتج</h4>
                    <p>اختبار وظيفة عرض تفاصيل المنتج</p>
                    <button class="test-button" onclick="testViewProduct()">اختبار العرض</button>
                    <div id="viewProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-edit"></i> تعديل المنتج</h4>
                    <p>اختبار وظيفة تعديل المنتج المحسنة</p>
                    <button class="test-button" onclick="testEditProduct()">اختبار التعديل</button>
                    <div id="editProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-trash"></i> حذف المنتج</h4>
                    <p>اختبار وظيفة حذف المنتج</p>
                    <button class="test-button" onclick="testDeleteProduct()">اختبار الحذف</button>
                    <div id="deleteProductResult"></div>
                </div>
                
                <div class="test-card">
                    <h4><i class="fas fa-rocket"></i> صفحة الهبوط</h4>
                    <p>اختبار وظيفة عرض صفحة الهبوط</p>
                    <button class="test-button" onclick="testViewLandingPage()">اختبار صفحة الهبوط</button>
                    <div id="viewLandingPageResult"></div>
                </div>
            </div>
        </div>

        <!-- Modal Display Test -->
        <div class="test-section">
            <h3><i class="fas fa-window-maximize"></i> اختبار عرض النوافذ المنبثقة</h3>
            <p>فحص ما إذا كانت النوافذ المنبثقة تعرض المحتوى بشكل صحيح</p>
            <button class="test-button" onclick="testModalDisplay()">اختبار النوافذ المنبثقة</button>
            <div id="modalDisplayResult"></div>
        </div>

        <!-- ReferenceError Prevention Test -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> اختبار منع أخطاء ReferenceError</h3>
            <p>التأكد من عدم وجود أخطاء ReferenceError عند استدعاء الوظائف</p>
            <button class="test-button" onclick="testReferenceErrorPrevention()">اختبار منع الأخطاء</button>
            <div id="referenceErrorResult"></div>
        </div>

        <!-- CSS and Layout Test -->
        <div class="test-section">
            <h3><i class="fas fa-paint-brush"></i> اختبار التخطيط والتصميم</h3>
            <p>فحص التخطيط العام والتصميم المتجاوب</p>
            <button class="test-button" onclick="testLayoutAndCSS()">اختبار التخطيط</button>
            <div id="layoutResult"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> مخرجات وحدة التحكم</h3>
            <div id="consoleOutput" class="console-output">جاهز لعرض مخرجات الاختبارات...</div>
        </div>

        <!-- Run All Tests -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" style="background: #28a745; font-size: 16px; padding: 15px 30px;" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
            <button class="test-button" style="background: #6c757d; font-size: 16px; padding: 15px 30px;" onclick="clearResults()">
                <i class="fas fa-eraser"></i> مسح النتائج
            </button>
        </div>
    </div>

    <!-- Demo Modal -->
    <div id="demoModal" class="demo-modal">
        <div class="demo-modal-content">
            <h3><i class="fas fa-info-circle"></i> نافذة تجريبية</h3>
            <p>هذه نافذة منبثقة تجريبية لاختبار عرض المحتوى.</p>
            <div style="margin: 20px 0;">
                <strong>اسم المنتج:</strong> منتج تجريبي<br>
                <strong>السعر:</strong> 2500.00 دج<br>
                <strong>الفئة:</strong> إلكترونيات<br>
                <strong>الحالة:</strong> نشط
            </div>
            <button class="test-button" onclick="closeDemoModal()">إغلاق</button>
        </div>
    </div>

    <!-- Load the JavaScript files -->
    <script src="js/product-management-functions.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/products-pagination.js"></script>

    <script>
        // Console capture for testing
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        let consoleOutput = '';

        function captureConsole() {
            console.log = function(...args) {
                consoleOutput += '[LOG] ' + args.join(' ') + '\n';
                originalConsole.log.apply(console, args);
                updateConsoleDisplay();
            };

            console.error = function(...args) {
                consoleOutput += '[ERROR] ' + args.join(' ') + '\n';
                originalConsole.error.apply(console, args);
                updateConsoleDisplay();
            };

            console.warn = function(...args) {
                consoleOutput += '[WARN] ' + args.join(' ') + '\n';
                originalConsole.warn.apply(console, args);
                updateConsoleDisplay();
            };
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('consoleOutput');
            if (consoleDiv) {
                consoleDiv.textContent = consoleOutput || 'لا توجد مخرجات...';
                consoleDiv.scrollTop = consoleDiv.scrollHeight;
            }
        }

        function clearConsole() {
            consoleOutput = '';
            updateConsoleDisplay();
        }

        // Test functions
        function testHeaderSpace() {
            const resultDiv = document.getElementById('headerSpaceResult');
            
            // Check for common header space issues
            const body = document.body;
            const bodyMargin = window.getComputedStyle(body).marginTop;
            const bodyPadding = window.getComputedStyle(body).paddingTop;
            
            let results = '<h4>نتائج فحص مساحة الرأس:</h4>';
            
            if (bodyMargin === '0px' && bodyPadding === '0px') {
                results += '<div class="test-success">✅ لا توجد مساحة إضافية في الرأس</div>';
            } else {
                results += `<div class="test-warning">⚠️ تم العثور على مساحة: margin-top: ${bodyMargin}, padding-top: ${bodyPadding}</div>`;
            }
            
            // Check if header-space-fix.css is loaded
            const stylesheets = Array.from(document.styleSheets);
            const headerFixLoaded = stylesheets.some(sheet => 
                sheet.href && sheet.href.includes('header-space-fix.css')
            );
            
            if (headerFixLoaded) {
                results += '<div class="test-success">✅ ملف إصلاح مساحة الرأس محمل</div>';
            } else {
                results += '<div class="test-error">❌ ملف إصلاح مساحة الرأس غير محمل</div>';
            }
            
            resultDiv.innerHTML = results;
        }

        function testViewProduct() {
            const resultDiv = document.getElementById('viewProductResult');
            
            try {
                if (typeof window.viewProduct === 'function') {
                    resultDiv.innerHTML = '<div class="test-success"><span class="status-indicator status-pass"></span>وظيفة العرض متاحة</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-error"><span class="status-indicator status-fail"></span>وظيفة العرض غير متاحة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error"><span class="status-indicator status-fail"></span>خطأ: ${error.message}</div>`;
            }
        }

        function testEditProduct() {
            const resultDiv = document.getElementById('editProductResult');
            
            try {
                if (typeof window.editProduct === 'function' && typeof window.showProductEditModal === 'function') {
                    resultDiv.innerHTML = '<div class="test-success"><span class="status-indicator status-pass"></span>وظائف التعديل المحسنة متاحة</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-error"><span class="status-indicator status-fail"></span>وظائف التعديل غير مكتملة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error"><span class="status-indicator status-fail"></span>خطأ: ${error.message}</div>`;
            }
        }

        function testDeleteProduct() {
            const resultDiv = document.getElementById('deleteProductResult');
            
            try {
                if (typeof window.deleteProduct === 'function') {
                    resultDiv.innerHTML = '<div class="test-success"><span class="status-indicator status-pass"></span>وظيفة الحذف متاحة</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-error"><span class="status-indicator status-fail"></span>وظيفة الحذف غير متاحة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error"><span class="status-indicator status-fail"></span>خطأ: ${error.message}</div>`;
            }
        }

        function testViewLandingPage() {
            const resultDiv = document.getElementById('viewLandingPageResult');
            
            try {
                if (typeof window.viewLandingPage === 'function') {
                    resultDiv.innerHTML = '<div class="test-success"><span class="status-indicator status-pass"></span>وظيفة صفحة الهبوط متاحة</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-error"><span class="status-indicator status-fail"></span>وظيفة صفحة الهبوط غير متاحة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-error"><span class="status-indicator status-fail"></span>خطأ: ${error.message}</div>`;
            }
        }

        function testModalDisplay() {
            const resultDiv = document.getElementById('modalDisplayResult');
            
            // Show demo modal
            const demoModal = document.getElementById('demoModal');
            demoModal.classList.add('show');
            
            setTimeout(() => {
                demoModal.classList.remove('show');
                resultDiv.innerHTML = '<div class="test-success">✅ النوافذ المنبثقة تعمل بشكل صحيح</div>';
            }, 2000);
            
            resultDiv.innerHTML = '<div class="test-warning">⏳ جاري اختبار النافذة المنبثقة...</div>';
        }

        function closeDemoModal() {
            document.getElementById('demoModal').classList.remove('show');
        }

        function testReferenceErrorPrevention() {
            const resultDiv = document.getElementById('referenceErrorResult');
            
            const functions = ['editProduct', 'viewProduct', 'deleteProduct', 'viewLandingPage'];
            let allAvailable = true;
            let results = '<h4>نتائج اختبار منع أخطاء ReferenceError:</h4>';
            
            functions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        results += `<div class="test-success">✅ ${funcName}: لن يحدث ReferenceError</div>`;
                    } else {
                        results += `<div class="test-error">❌ ${funcName}: سيحدث ReferenceError</div>`;
                        allAvailable = false;
                    }
                } catch (error) {
                    results += `<div class="test-error">❌ ${funcName}: خطأ - ${error.message}</div>`;
                    allAvailable = false;
                }
            });
            
            if (allAvailable) {
                results += '<div class="test-success"><strong>🎉 تم منع جميع أخطاء ReferenceError بنجاح!</strong></div>';
            } else {
                results += '<div class="test-error"><strong>⚠️ لا تزال هناك مشاكل ReferenceError</strong></div>';
            }
            
            resultDiv.innerHTML = results;
        }

        function testLayoutAndCSS() {
            const resultDiv = document.getElementById('layoutResult');
            
            let results = '<h4>نتائج اختبار التخطيط:</h4>';
            
            // Check RTL direction
            const direction = document.documentElement.dir;
            if (direction === 'rtl') {
                results += '<div class="test-success">✅ اتجاه RTL مطبق بشكل صحيح</div>';
            } else {
                results += '<div class="test-error">❌ اتجاه RTL غير مطبق</div>';
            }
            
            // Check responsive design
            const viewport = window.innerWidth;
            if (viewport > 768) {
                results += '<div class="test-success">✅ التصميم متجاوب للشاشات الكبيرة</div>';
            } else {
                results += '<div class="test-success">✅ التصميم متجاوب للشاشات الصغيرة</div>';
            }
            
            resultDiv.innerHTML = results;
        }

        function runAllTests() {
            clearConsole();
            console.log('🚀 بدء تشغيل جميع اختبارات إصلاحات إدارة المنتجات...');
            
            testHeaderSpace();
            testViewProduct();
            testEditProduct();
            testDeleteProduct();
            testViewLandingPage();
            testModalDisplay();
            testReferenceErrorPrevention();
            testLayoutAndCSS();
            
            console.log('✅ انتهاء جميع الاختبارات.');
        }

        function clearResults() {
            const resultDivs = [
                'headerSpaceResult', 'viewProductResult', 'editProductResult', 
                'deleteProductResult', 'viewLandingPageResult', 'modalDisplayResult',
                'referenceErrorResult', 'layoutResult'
            ];
            
            resultDivs.forEach(id => {
                const div = document.getElementById(id);
                if (div) div.innerHTML = '';
            });
            
            clearConsole();
        }

        // Initialize console capture
        captureConsole();
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
