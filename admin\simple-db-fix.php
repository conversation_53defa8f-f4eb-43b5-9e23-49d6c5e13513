<?php
/**
 * Simple Database Fix
 */

// Database connection
$host = 'localhost';
$dbname = 'poultraydz';
$username = 'postgres';
$password = 'root';

try {
    $pdo = new PDO("pgsql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connected successfully\n";
    
    // Check if role_permissions table exists
    $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'role_permissions'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "Creating role_permissions table...\n";
        
        $createTable = "
        CREATE TABLE role_permissions (
            id SERIAL PRIMARY KEY,
            role_id INTEGER NOT NULL,
            permission_id INTEGER NOT NULL,
            granted BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            UNIQUE(role_id, permission_id)
        )";
        
        $pdo->exec($createTable);
        echo "✅ role_permissions table created\n";
    } else {
        echo "✅ role_permissions table exists\n";
        
        // Check if granted column exists
        $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'role_permissions' AND column_name = 'granted'");
        $hasGranted = $stmt->rowCount() > 0;
        
        if (!$hasGranted) {
            echo "Adding granted column...\n";
            $pdo->exec("ALTER TABLE role_permissions ADD COLUMN granted BOOLEAN DEFAULT TRUE");
            echo "✅ granted column added\n";
        } else {
            echo "✅ granted column exists\n";
        }
    }
    
    echo "🎉 Database schema fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
