<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات العامة</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-family: inherit;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .quick-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .settings-demo {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .setting-key {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #495057;
        }
        
        .setting-value {
            color: #28a745;
            font-weight: 500;
        }
        
        .category-section {
            margin-bottom: 20px;
        }
        
        .category-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #e9ecef;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cogs"></i>
                اختبار الإعدادات العامة
            </h1>
            <p>فحص شامل لجميع مكونات نظام الإعدادات العامة</p>
        </div>

        <div class="test-grid">
            <!-- API Test -->
            <div class="test-section">
                <h3><i class="fas fa-plug"></i> اختبار API الإعدادات</h3>
                <p>فحص جميع نقاط API لإدارة الإعدادات العامة</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="testSettingsAPI()">
                        <i class="fas fa-play"></i>
                        اختبار API
                    </button>
                </div>

                <div class="loading" id="apiLoading">
                    <div class="spinner"></div>
                    <p>جاري فحص API...</p>
                </div>

                <div class="results" id="apiResults" style="display: none;">
                    <h4>نتائج فحص API</h4>
                    <div id="apiResultsList"></div>
                </div>
            </div>

            <!-- Settings Categories -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> فئات الإعدادات</h3>
                <p>عرض جميع فئات الإعدادات المتاحة</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-success" onclick="loadCategories()">
                        <i class="fas fa-folder"></i>
                        تحميل الفئات
                    </button>
                </div>

                <div class="loading" id="categoriesLoading">
                    <div class="spinner"></div>
                    <p>جاري تحميل الفئات...</p>
                </div>

                <div class="results" id="categoriesResults" style="display: none;">
                    <h4>فئات الإعدادات</h4>
                    <div id="categoriesList"></div>
                </div>
            </div>

            <!-- Settings Demo -->
            <div class="test-section">
                <h3><i class="fas fa-eye"></i> عرض الإعدادات</h3>
                <p>عرض الإعدادات الحالية مجمعة حسب الفئة</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-warning" onclick="loadSettings()">
                        <i class="fas fa-download"></i>
                        تحميل الإعدادات
                    </button>
                </div>

                <div class="loading" id="settingsLoading">
                    <div class="spinner"></div>
                    <p>جاري تحميل الإعدادات...</p>
                </div>

                <div class="settings-demo" id="settingsDemo" style="display: none;">
                    <h4>الإعدادات الحالية</h4>
                    <div id="settingsList"></div>
                </div>
            </div>

            <!-- Bulk Operations -->
            <div class="test-section">
                <h3><i class="fas fa-tasks"></i> العمليات المجمعة</h3>
                <p>اختبار العمليات المجمعة والنسخ الاحتياطي</p>
                
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="testBulkOperations()">
                        <i class="fas fa-cogs"></i>
                        اختبار العمليات
                    </button>
                    <button class="btn btn-success" onclick="testBackup()">
                        <i class="fas fa-download"></i>
                        اختبار النسخ الاحتياطي
                    </button>
                </div>

                <div class="loading" id="bulkLoading">
                    <div class="spinner"></div>
                    <p>جاري تنفيذ العمليات...</p>
                </div>

                <div class="results" id="bulkResults" style="display: none;">
                    <h4>نتائج العمليات المجمعة</h4>
                    <div id="bulkResultsList"></div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <a href="admin/system-settings/general-settings.html" class="btn btn-primary" target="_blank">
                <i class="fas fa-cogs"></i>
                الإعدادات العامة
            </a>
            <a href="admin/system-settings/index.html" class="btn btn-success" target="_blank">
                <i class="fas fa-home"></i>
                إعدادات النظام
            </a>
            <a href="database/setup-test.html" class="btn btn-warning" target="_blank">
                <i class="fas fa-database"></i>
                إعداد قاعدة البيانات
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'api/system/general-settings.php';

        // Test Settings API
        async function testSettingsAPI() {
            const loading = document.getElementById('apiLoading');
            const results = document.getElementById('apiResults');
            const resultsList = document.getElementById('apiResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            const endpoints = [
                { name: 'جلب الإعدادات', url: `${API_BASE}`, method: 'GET' },
                { name: 'جلب الفئات', url: `${API_BASE}?action=categories`, method: 'GET' },
                { name: 'إعدادات الموقع', url: `${API_BASE}?category=site`, method: 'GET' },
                { name: 'البحث في الإعدادات', url: `${API_BASE}?search=site`, method: 'GET' }
            ];
            
            resultsList.innerHTML = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    const data = await response.json();
                    
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status ${data.success ? 'success' : 'error'}">
                            ${data.success ? 'يعمل' : 'خطأ'}
                        </span>
                    `;
                    resultsList.appendChild(item);
                    
                } catch (error) {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span><strong>${endpoint.name}</strong></span>
                        <span class="status error">فشل</span>
                    `;
                    resultsList.appendChild(item);
                }
            }
            
            loading.classList.remove('show');
            results.style.display = 'block';
        }

        // Load Categories
        async function loadCategories() {
            const loading = document.getElementById('categoriesLoading');
            const results = document.getElementById('categoriesResults');
            const categoriesList = document.getElementById('categoriesList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            try {
                const response = await fetch(`${API_BASE}?action=categories`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    categoriesList.innerHTML = '';
                    
                    data.data.forEach(category => {
                        const item = document.createElement('div');
                        item.className = 'result-item';
                        item.innerHTML = `
                            <span><strong>${category.name_ar}</strong> (${category.category})</span>
                            <span class="status success">${category.count} إعداد</span>
                        `;
                        categoriesList.appendChild(item);
                    });
                    
                    results.style.display = 'block';
                } else {
                    showNotification('فشل في تحميل الفئات', 'error');
                }
                
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Load Settings
        async function loadSettings() {
            const loading = document.getElementById('settingsLoading');
            const demo = document.getElementById('settingsDemo');
            const settingsList = document.getElementById('settingsList');
            
            loading.classList.add('show');
            demo.style.display = 'none';
            
            try {
                const response = await fetch(API_BASE);
                const data = await response.json();
                
                if (data.success && data.data) {
                    settingsList.innerHTML = '';
                    
                    Object.keys(data.data).forEach(category => {
                        const categoryDiv = document.createElement('div');
                        categoryDiv.className = 'category-section';
                        
                        const categoryTitle = document.createElement('div');
                        categoryTitle.className = 'category-title';
                        categoryTitle.textContent = getCategoryName(category);
                        categoryDiv.appendChild(categoryTitle);
                        
                        data.data[category].forEach(setting => {
                            const settingDiv = document.createElement('div');
                            settingDiv.className = 'setting-item';
                            
                            let displayValue = setting.setting_value;
                            if (setting.data_type === 'boolean') {
                                displayValue = setting.setting_value ? 'مفعل' : 'معطل';
                            } else if (setting.data_type === 'json') {
                                displayValue = 'JSON Object';
                            } else if (typeof displayValue === 'string' && displayValue.length > 50) {
                                displayValue = displayValue.substring(0, 50) + '...';
                            }
                            
                            settingDiv.innerHTML = `
                                <div>
                                    <div class="setting-key">${setting.setting_key}</div>
                                    <small>${setting.description_ar}</small>
                                </div>
                                <div class="setting-value">${displayValue}</div>
                            `;
                            categoryDiv.appendChild(settingDiv);
                        });
                        
                        settingsList.appendChild(categoryDiv);
                    });
                    
                    demo.style.display = 'block';
                } else {
                    showNotification('فشل في تحميل الإعدادات', 'error');
                }
                
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Test Bulk Operations
        async function testBulkOperations() {
            const loading = document.getElementById('bulkLoading');
            const results = document.getElementById('bulkResults');
            const resultsList = document.getElementById('bulkResultsList');
            
            loading.classList.add('show');
            results.style.display = 'none';
            
            try {
                // Test bulk update
                const testSettings = {
                    'site_name': 'موقع تجريبي',
                    'site_description': 'وصف تجريبي للموقع'
                };
                
                const response = await fetch(`${API_BASE}?action=bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ settings: testSettings })
                });
                
                const data = await response.json();
                
                resultsList.innerHTML = '';
                
                const item = document.createElement('div');
                item.className = 'result-item';
                item.innerHTML = `
                    <span><strong>التحديث المجمع</strong> - ${data.updated_count || 0} إعداد</span>
                    <span class="status ${data.success ? 'success' : 'error'}">
                        ${data.success ? 'نجح' : 'فشل'}
                    </span>
                `;
                resultsList.appendChild(item);
                
                if (data.errors && data.errors.length > 0) {
                    data.errors.forEach(error => {
                        const errorItem = document.createElement('div');
                        errorItem.className = 'result-item';
                        errorItem.innerHTML = `
                            <span><strong>خطأ:</strong> ${error}</span>
                            <span class="status warning">تحذير</span>
                        `;
                        resultsList.appendChild(errorItem);
                    });
                }
                
                results.style.display = 'block';
                
            } catch (error) {
                showNotification('فشل في اختبار العمليات المجمعة: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Test Backup
        async function testBackup() {
            try {
                showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');
                
                const response = await fetch(`${API_BASE}?action=backup`);
                
                if (response.ok) {
                    showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                } else {
                    throw new Error('فشل في إنشاء النسخة الاحتياطية');
                }
                
            } catch (error) {
                showNotification('فشل في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
            }
        }

        // Helper Functions
        function getCategoryName(category) {
            const names = {
                'site': 'إعدادات الموقع',
                'email': 'إعدادات البريد الإلكتروني',
                'sms': 'إعدادات الرسائل النصية',
                'social': 'وسائل التواصل الاجتماعي',
                'seo': 'تحسين محركات البحث',
                'analytics': 'التحليلات والإحصائيات',
                'maintenance': 'الصيانة والنسخ الاحتياطي',
                'localization': 'التوطين واللغة',
                'api': 'إعدادات API',
                'system': 'إعدادات النظام'
            };
            return names[category] || category;
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
