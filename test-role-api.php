<?php
/**
 * Test Role Management API
 * اختبار API إدارة الأدوار
 */

header('Content-Type: application/json; charset=utf-8');

// Test the roles API endpoint
$apiUrl = 'http://localhost:8080/php/api/roles.php?action=list';

echo "Testing Role Management API...\n";
echo "اختبار API إدارة الأدوار...\n\n";

try {
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Status Code: $httpCode\n";
    echo "رمز حالة HTTP: $httpCode\n\n";
    
    if ($error) {
        echo "cURL Error: $error\n";
        echo "خطأ cURL: $error\n";
        exit;
    }
    
    if ($httpCode === 200) {
        echo "✅ API Response received successfully\n";
        echo "✅ تم استلام استجابة API بنجاح\n\n";
        
        $data = json_decode($response, true);
        
        if ($data) {
            echo "Response Data:\n";
            echo "بيانات الاستجابة:\n";
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
            
            if (isset($data['success']) && $data['success']) {
                echo "✅ API call successful\n";
                echo "✅ استدعاء API نجح\n";
                
                if (isset($data['roles']) && is_array($data['roles'])) {
                    $rolesCount = count($data['roles']);
                    echo "📊 Roles found: $rolesCount\n";
                    echo "📊 الأدوار الموجودة: $rolesCount\n";
                    
                    if ($rolesCount > 0) {
                        echo "\nRole Details:\n";
                        echo "تفاصيل الأدوار:\n";
                        foreach ($data['roles'] as $role) {
                            echo "- ID: {$role['id']}, Name: {$role['name']}, Display: {$role['display_name_ar']}\n";
                        }
                    }
                } else {
                    echo "⚠️ No roles data in response\n";
                    echo "⚠️ لا توجد بيانات أدوار في الاستجابة\n";
                }
            } else {
                echo "❌ API call failed\n";
                echo "❌ فشل استدعاء API\n";
                if (isset($data['message'])) {
                    echo "Error: {$data['message']}\n";
                    echo "خطأ: {$data['message']}\n";
                }
            }
        } else {
            echo "❌ Invalid JSON response\n";
            echo "❌ استجابة JSON غير صالحة\n";
            echo "Raw response: $response\n";
        }
    } else {
        echo "❌ HTTP Error: $httpCode\n";
        echo "❌ خطأ HTTP: $httpCode\n";
        echo "Response: $response\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "❌ استثناء: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "=== اكتمل الاختبار ===\n";
?>
